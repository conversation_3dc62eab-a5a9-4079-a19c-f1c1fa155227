﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Test" xml:space="preserve">
    <value>테스트</value>
  </data>
  <data name="AddNews" xml:space="preserve">
    <value>뉴스 추가</value>
  </data>
  <data name="NewsTitleDesc" xml:space="preserve">
    <value>뉴스</value>
  </data>
  <data name="BtnEdit" xml:space="preserve">
    <value>편집</value>
  </data>
  <data name="BtnDetails" xml:space="preserve">
    <value>세부 정보</value>
  </data>
  <data name="BtnDelete" xml:space="preserve">
    <value>삭제</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>총</value>
  </data>
  <data name="News" xml:space="preserve">
    <value>뉴스</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>연락처</value>
  </data>
  <data name="OwnerTitle" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="EditTitle" xml:space="preserve">
    <value>편집</value>
  </data>
  <data name="BackToList" xml:space="preserve">
    <value>목록으로 돌아가기</value>
  </data>
  <data name="BtnSave" xml:space="preserve">
    <value>저장</value>
  </data>
  <data name="Lang" xml:space="preserve">
    <value>en</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>언어</value>
  </data>
  <data name="LangCode" xml:space="preserve">
    <value>En</value>
  </data>
  <data name="LangDesc" xml:space="preserve">
    <value>영어</value>
  </data>
  <data name="Width" xml:space="preserve">
    <value>너비</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>지역</value>
  </data>
  <data name="CreateNew" xml:space="preserve">
    <value>새로 만들기</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>코드</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>제목</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>설명</value>
  </data>
  <data name="MapZoom" xml:space="preserve">
    <value>지도 확대/축소</value>
  </data>
  <data name="MapCenterY" xml:space="preserve">
    <value>지도 센터 Y</value>
  </data>
  <data name="MapCenterX" xml:space="preserve">
    <value>지도 센터 X</value>
  </data>
  <data name="RegionsTitleDesc" xml:space="preserve">
    <value>모바일 애플 리 케이 션 내부 지역</value>
  </data>
  <data name="TitleDetails" xml:space="preserve">
    <value>세부 정보</value>
  </data>
  <data name="CreateTitle" xml:space="preserve">
    <value>만들기</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>추가</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>당신은 이것을 삭제 하 시겠습니까?</value>
  </data>
  <data name="DeleteTitle" xml:space="preserve">
    <value>삭제</value>
  </data>
  <data name="DividerOr" xml:space="preserve">
    <value>또는</value>
  </data>
  <data name="AddRegion" xml:space="preserve">
    <value>영역 추가</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>추가</value>
  </data>
  <data name="YourSecCode" xml:space="preserve">
    <value>보안 코드는:</value>
  </data>
  <data name="EmailFrom" xml:space="preserve">
    <value>사진 컨트롤 패널의 예술</value>
  </data>
  <data name="EmailCreateAccSubject" xml:space="preserve">
    <value>사진 예술의: 계정 생성 확인</value>
  </data>
  <data name="EmailCreateAccBody" xml:space="preserve">
    <value>사진 예술 사용자 계정을 &lt;br&gt;만들 요청을 받았다합니다
귀하의 이메일 주소 ({0})를 사용 하 여. &lt;br&gt;
&lt;br&gt;
계속 하려면이 이메일 주소를 사용 하 여 계정을 생성는 &lt;br&gt;방문
다음 링크: &lt;br&gt;
&lt;br&gt;
&lt;a href="{1}" target="blank" rel="noopener"&gt; {1}&lt;/a&gt; &lt;br&gt;
&lt;br&gt;
계정을 생성 하려는 경우 또는이 요청 &lt;br&gt;로 이루어진 경우
단지이 메시지를 무시할 수 있습니다 하는 중 오류가 발생 했습니다. &lt;br&gt;
&lt;br&gt;
만약 위의 링크는 작동 하지 않습니다, 또는 당신은 &lt;br&gt;와 관련 된 다른 문제
귀하의 계정 관리 ***************에 문의 하시기 바랍니다. &lt;br&gt;
&lt;br&gt;</value>
  </data>
  <data name="AccCreationTitle" xml:space="preserve">
    <value>계정 만들기</value>
  </data>
  <data name="AccCeationConfirmEmail" xml:space="preserve">
    <value>우리는 당신이 다음 이메일 주소에 이메일 보냈습니다: {0}.
등록을 완료 하려면이 이메일에 지시를 따르시기 바랍니다.</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>지역</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>시간</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>텍스트</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>액션</value>
  </data>
  <data name="Parameters" xml:space="preserve">
    <value>매개 변수</value>
  </data>
  <data name="ImageURL" xml:space="preserve">
    <value>이미지 (url)</value>
  </data>
  <data name="Author" xml:space="preserve">
    <value>저자</value>
  </data>
  <data name="EditedBy" xml:space="preserve">
    <value>에 의해 편집</value>
  </data>
  <data name="EditedTime" xml:space="preserve">
    <value>편집된 시간</value>
  </data>
  <data name="ImageHeight" xml:space="preserve">
    <value>이미지 높이</value>
  </data>
  <data name="ImageWidth" xml:space="preserve">
    <value>이미지 폭</value>
  </data>
  <data name="ThankYouForConfirmingYourEmailPlease" xml:space="preserve">
    <value>귀하의 이메일을 확인 해 주셔서 감사 합니다. 제발</value>
  </data>
  <data name="ClickHereToLogIn" xml:space="preserve">
    <value>로그인 하려면 여기를 클릭 하십시오.</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>등록</value>
  </data>
  <data name="YouVeSuccessfullyAuthenticatedWith" xml:space="preserve">
    <value>당신이 성공적으로 인증 했습니다.</value>
  </data>
  <data name="PleaseEnterAUserNameForThisSiteBelow" xml:space="preserve">
    <value>아래에이 사이트에 대 한 사용자 이름을 입력 하 고 로그인 완료 등록 버튼을 클릭 하십시오.</value>
  </data>
  <data name="RegisterTitle" xml:space="preserve">
    <value>등록</value>
  </data>
  <data name="AssociateYourAccount" xml:space="preserve">
    <value>{0} 계정에 연결 합니다.</value>
  </data>
  <data name="UnsuccessfulLoginWithService" xml:space="preserve">
    <value>실패 한 로그인 서비스입니다.</value>
  </data>
  <data name="LoginFailure" xml:space="preserve">
    <value>로그인 실패</value>
  </data>
  <data name="LogIn" xml:space="preserve">
    <value>로그인</value>
  </data>
  <data name="OrUseAnotherServiceToLogIn" xml:space="preserve">
    <value>하거나 다른 서비스를 사용 하 여 로그인</value>
  </data>
  <data name="UseALocalAccountToLogIn" xml:space="preserve">
    <value>로컬 계정을 사용 하 여</value>
  </data>
  <data name="RememberMe" xml:space="preserve">
    <value>나를 기억합니다</value>
  </data>
  <data name="BtnLogIn" xml:space="preserve">
    <value>로그인</value>
  </data>
  <data name="RegisterAsANewUser" xml:space="preserve">
    <value>새 계정 만들기</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>암호</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>전자 메일</value>
  </data>
  <data name="ConfirmPassword" xml:space="preserve">
    <value>암호 확인</value>
  </data>
  <data name="CreateANewAccount" xml:space="preserve">
    <value>새 계정 만들기</value>
  </data>
  <data name="BtnRegister" xml:space="preserve">
    <value>등록</value>
  </data>
  <data name="ToolbarLogin" xml:space="preserve">
    <value>로그인</value>
  </data>
  <data name="ToolbarRegister" xml:space="preserve">
    <value>등록</value>
  </data>
  <data name="ToolbarHello" xml:space="preserve">
    <value>안녕하세요</value>
  </data>
  <data name="ToolbarLogoff" xml:space="preserve">
    <value>(잊지 마세요) 로그 오프</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>그래</value>
  </data>
  <data name="MoreInfo" xml:space="preserve">
    <value>더 많은 정보</value>
  </data>
  <data name="OnMap" xml:space="preserve">
    <value>지도에</value>
  </data>
  <data name="Centers" xml:space="preserve">
    <value>센터</value>
  </data>
  <data name="Adress" xml:space="preserve">
    <value>주소</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>노트</value>
  </data>
  <data name="Site" xml:space="preserve">
    <value>웹사이트</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>전화</value>
  </data>
  <data name="Mail" xml:space="preserve">
    <value>메일</value>
  </data>
  <data name="Metro" xml:space="preserve">
    <value>메트로</value>
  </data>
  <data name="ExportedBy" xml:space="preserve">
    <value>에 의해 ExportNeeded</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>상태</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>활성</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>비활성</value>
  </data>
  <data name="ExportedTime" xml:space="preserve">
    <value>ExportNeeded</value>
  </data>
  <data name="Subtitle" xml:space="preserve">
    <value>자막</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>도시</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>국가</value>
  </data>
  <data name="Uploads" xml:space="preserve">
    <value>파일</value>
  </data>
  <data name="UploadImage" xml:space="preserve">
    <value>이미지 업로드</value>
  </data>
  <data name="SearchByName" xml:space="preserve">
    <value>이름으로 검색</value>
  </data>
  <data name="Export" xml:space="preserve">
    <value>내보내기</value>
  </data>
  <data name="NotAllowed" xml:space="preserve">
    <value>허용 되지 않습니다.</value>
  </data>
  <data name="Allowed" xml:space="preserve">
    <value>허용</value>
  </data>
  <data name="Needed" xml:space="preserve">
    <value>필요!</value>
  </data>
  <data name="ToBeExported" xml:space="preserve">
    <value>내보낼 수</value>
  </data>
  <data name="HelpAllowToBeExportedForMobileAppOrNot" xml:space="preserve">
    <value>아닌지 모바일 애플 리 케이 션에 대 한 내보낼 수 있습니다.</value>
  </data>
  <data name="SortList" xml:space="preserve">
    <value>정렬 목록</value>
  </data>
  <data name="SortAbc" xml:space="preserve">
    <value>Abc에 의해 정렬</value>
  </data>
  <data name="SortDate" xml:space="preserve">
    <value>버전 날짜</value>
  </data>
  <data name="NewsController_Create_ERRORUImageURLNotValid" xml:space="preserve">
    <value>오류: 이미지 URL 유효 하지 않습니다!</value>
  </data>
  <data name="OwnerTitleShort" xml:space="preserve">
    <value>Art of Foto</value>
  </data>
  <data name="AppoMobiControlPanel" xml:space="preserve">
    <value>제어 패널</value>
  </data>
  <data name="Exports" xml:space="preserve">
    <value>수출</value>
  </data>
  <data name="CreateExportFor" xml:space="preserve">
    <value>대 한 수출을 실행:</value>
  </data>
  <data name="ExportType" xml:space="preserve">
    <value>내보내기 형식</value>
  </data>
  <data name="Denied" xml:space="preserve">
    <value>액세스 거부</value>
  </data>
  <data name="DonTHaveTheRights" xml:space="preserve">
    <value>그것은 없는 권리 acces이 섹션을 나타납니다. 당신이 생각 하는 실수 있을 경우 지원에 문의 하십시오.</value>
  </data>
  <data name="Exporting" xml:space="preserve">
    <value>내보내기</value>
  </data>
  <data name="ConfirmExport" xml:space="preserve">
    <value>당신은 이것을 내보낼 하 시겠습니까?</value>
  </data>
  <data name="ExportsController_Index_ExportComplete" xml:space="preserve">
    <value>내보내기 완료!</value>
  </data>
  <data name="BaseURL" xml:space="preserve">
    <value>기본 URL</value>
  </data>
  <data name="SalonList" xml:space="preserve">
    <value>살롱 목록</value>
  </data>
  <data name="InSection" xml:space="preserve">
    <value>내부</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>모든</value>
  </data>
  <data name="ShouldNotBeExported" xml:space="preserve">
    <value>내보낼 수 없습니다.</value>
  </data>
  <data name="WasWellExported" xml:space="preserve">
    <value>수출 잘 했다</value>
  </data>
  <data name="ShouldBeExported" xml:space="preserve">
    <value>수출 한다</value>
  </data>
  <data name="InRegion" xml:space="preserve">
    <value>지역에서</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>에 의해</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>제품</value>
  </data>
  <data name="Categories" xml:space="preserve">
    <value>카테고리</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>시스템</value>
  </data>
  <data name="RoleSuperuser" xml:space="preserve">
    <value>수퍼유저</value>
  </data>
  <data name="RoleAdmin" xml:space="preserve">
    <value>관리자</value>
  </data>
  <data name="RoleEditor" xml:space="preserve">
    <value>편집기</value>
  </data>
  <data name="RoleNoRole" xml:space="preserve">
    <value>게스트</value>
  </data>
  <data name="Parent" xml:space="preserve">
    <value>부모</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>우선 순위</value>
  </data>
  <data name="SortDefault" xml:space="preserve">
    <value>기본적으로</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>정보</value>
  </data>
  <data name="Subcategories" xml:space="preserve">
    <value>하위 범주</value>
  </data>
  <data name="ParentElementToInsertCategoryInto" xml:space="preserve">
    <value>부모 범주에이 요소를 삽입 하려면</value>
  </data>
  <data name="RootCategory" xml:space="preserve">
    <value>루트 카테고리</value>
  </data>
  <data name="CatNewsSlider" xml:space="preserve">
    <value>뉴스 슬라이더</value>
  </data>
  <data name="CatSecRoot" xml:space="preserve">
    <value>2 차 기본 카테고리</value>
  </data>
  <data name="UploadMiniImage" xml:space="preserve">
    <value>미니 이미지 업로드</value>
  </data>
  <data name="ImageURLForMini" xml:space="preserve">
    <value>미니 이미지 URL</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>카테고리</value>
  </data>
  <data name="Volume" xml:space="preserve">
    <value>볼륨</value>
  </data>
  <data name="Recommendation" xml:space="preserve">
    <value>Thalion 추천</value>
  </data>
  <data name="ILike" xml:space="preserve">
    <value>나 처럼</value>
  </data>
  <data name="Units" xml:space="preserve">
    <value>단위</value>
  </data>
  <data name="Keywords" xml:space="preserve">
    <value>키워드</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>새로운</value>
  </data>
  <data name="ShowList" xml:space="preserve">
    <value>보기</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>검색...</value>
  </data>
  <data name="ProductsController_CreateDropdownList_ANYCAT" xml:space="preserve">
    <value>모든 카테고리</value>
  </data>
  <data name="SortCode" xml:space="preserve">
    <value>코드에 의해</value>
  </data>
  <data name="SortCat" xml:space="preserve">
    <value>카테고리별</value>
  </data>
  <data name="RoleMerchandiser" xml:space="preserve">
    <value>상인</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>리셋</value>
  </data>
  <data name="CatRoot2" xml:space="preserve">
    <value>기본 카테고리 2</value>
  </data>
  <data name="EnterReason" xml:space="preserve">
    <value>입력 하는 이유</value>
  </data>
  <data name="FeaturedImage" xml:space="preserve">
    <value>추천된 이미지</value>
  </data>
  <data name="Body" xml:space="preserve">
    <value>바디</value>
  </data>
  <data name="Face" xml:space="preserve">
    <value>얼굴</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>예</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>아니요</value>
  </data>
  <data name="OurChoice" xml:space="preserve">
    <value>우리의 선택</value>
  </data>
  <data name="ForgotYourPassword" xml:space="preserve">
    <value>비밀 번호 분실?</value>
  </data>
  <data name="NoRUTranslation" xml:space="preserve">
    <value>러시아어 번역</value>
  </data>
  <data name="ErrorNotFound" xml:space="preserve">
    <value>찾을 수 없습니다 오류</value>
  </data>
  <data name="ErrorUnknown" xml:space="preserve">
    <value>알 수 없는 오류</value>
  </data>
  <data name="Links" xml:space="preserve">
    <value>링크</value>
  </data>
  <data name="Redirect" xml:space="preserve">
    <value>링크</value>
  </data>
  <data name="Clicks" xml:space="preserve">
    <value>클릭 총</value>
  </data>
  <data name="AreYouSureToDelete" xml:space="preserve">
    <value>당신은 삭제 해야</value>
  </data>
  <data name="Treatment" xml:space="preserve">
    <value>치료</value>
  </data>
  <data name="Treatments" xml:space="preserve">
    <value>트리 트 먼 트</value>
  </data>
  <data name="ErrorPleaseCheckRequirementsForFieldsBelow" xml:space="preserve">
    <value>오류: 아래 필드에 대 한 요구 사항을 확인 하시기 바랍니다.</value>
  </data>
  <data name="Items" xml:space="preserve">
    <value>항목</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>관리</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>사용자</value>
  </data>
  <data name="ResetChanges" xml:space="preserve">
    <value>변경 취소</value>
  </data>
  <data name="DoNotSave" xml:space="preserve">
    <value>저장 하지 않습니다</value>
  </data>
  <data name="List" xml:space="preserve">
    <value>목록</value>
  </data>
  <data name="EditorSNotesInternalUseOnly" xml:space="preserve">
    <value>에디터의 노트, 내부만 사용</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>오류</value>
  </data>
  <data name="ControlPanelHtml" xml:space="preserve">
    <value>&lt;strong&gt;사진 예술&lt;/strong&gt; 제어 패널</value>
  </data>
  <data name="RememberMe2" xml:space="preserve">
    <value>기억 나?</value>
  </data>
  <data name="DidYouRememberYourPassword" xml:space="preserve">
    <value>당신은 비밀 번호를 기억 했는가?</value>
  </data>
  <data name="BtnResetPassword" xml:space="preserve">
    <value>암호 다시 설정</value>
  </data>
  <data name="PleaseCheckYourEmailToResetYourPassword" xml:space="preserve">
    <value>암호를 다시 설정 하려면 귀하의 이메일을 확인 하시기 바랍니다.</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>암호 다시 설정</value>
  </data>
  <data name="DoYouHaveAnAccount" xml:space="preserve">
    <value>계정을 당신은?</value>
  </data>
  <data name="RegisterAccount" xml:space="preserve">
    <value>계정 등록</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>마지막 이름</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>이름</value>
  </data>
  <data name="AgreeToTerms" xml:space="preserve">
    <value>&lt;strong&gt;약관&lt;/strong&gt;에 동의</value>
  </data>
  <data name="YouMustAcceptTermsAndConditions" xml:space="preserve">
    <value>약관 동의 해야 합니다.</value>
  </data>
  <data name="PasswordAndConfirmationPasswordDoNotMatch" xml:space="preserve">
    <value>암호와 확인 암호는 일치 하지 않습니다.</value>
  </data>
  <data name="StringLengthError" xml:space="preserve">
    <value>{0} 이어야 합니다 적어도 {2} 자.</value>
  </data>
  <data name="BadUsernameOrPassword" xml:space="preserve">
    <value>사용자 이름 또는 입력 한 암호가 올바르지 않습니다.</value>
  </data>
  <data name="EmailAlreadyTaken" xml:space="preserve">
    <value>이메일 ' {0} ' 이미.</value>
  </data>
  <data name="MailSubjectResetPassword" xml:space="preserve">
    <value>암호 다시 설정</value>
  </data>
  <data name="ResetYourPasswordMailBody" xml:space="preserve">
    <value>클릭 하 여 암호를 재설정 하십시오 &lt;a href="{0}"&gt; 여기&lt;/a&gt;.</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>새 암호</value>
  </data>
  <data name="PleaseEnterYourNewPasswordBelow" xml:space="preserve">
    <value>제발 아래 새 비밀 번호를 입력 합니다.</value>
  </data>
  <data name="DidYouRememberYourOLDPassword" xml:space="preserve">
    <value>당신은 당신의 이전 암호를 기억 했는가?</value>
  </data>
  <data name="AccountController_ResetPassword_InvalidToken" xml:space="preserve">
    <value>리셋 링크 만료 되었습니다.</value>
  </data>
  <data name="YourNewPasswordHasBeenSet" xml:space="preserve">
    <value>새 암호를 설정 했습니다.</value>
  </data>
  <data name="Class" xml:space="preserve">
    <value>클래스</value>
  </data>
  <data name="PrivacyPolicy" xml:space="preserve">
    <value>개인 정보 보호</value>
  </data>
  <data name="Published" xml:space="preserve">
    <value>게시</value>
  </data>
  <data name="FastLoginWith" xml:space="preserve">
    <value>빠른 로그인</value>
  </data>
  <data name="OrEnterYourCredentials" xml:space="preserve">
    <value>자격 증명을 입력 하는 또는</value>
  </data>
  <data name="Settings" xml:space="preserve">
    <value>설정</value>
  </data>
  <data name="ExternalLogins" xml:space="preserve">
    <value>외부 로그인</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>없음</value>
  </data>
  <data name="ChangeYourPassword" xml:space="preserve">
    <value>비밀 번호를 변경</value>
  </data>
  <data name="ManageAccount" xml:space="preserve">
    <value>계정 관리</value>
  </data>
  <data name="Change" xml:space="preserve">
    <value>변경</value>
  </data>
  <data name="TwoFactorAuthentication" xml:space="preserve">
    <value>2 단계 인증</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>귀하의 전화 번호</value>
  </data>
  <data name="Disabled" xml:space="preserve">
    <value>사용 안 함</value>
  </data>
  <data name="Enabled" xml:space="preserve">
    <value>사용 가능</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>활성화</value>
  </data>
  <data name="Manage" xml:space="preserve">
    <value>관리</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>사용 안 함</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>제거</value>
  </data>
  <data name="ManageYourExternalLogins" xml:space="preserve">
    <value>외부 로그인 관리</value>
  </data>
  <data name="X_Theme" xml:space="preserve">
    <value>테마</value>
  </data>
  <data name="Key" xml:space="preserve">
    <value>키</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>닫기</value>
  </data>
  <data name="ThankYou" xml:space="preserve">
    <value>감사합니다!</value>
  </data>
  <data name="EmailConfirmed" xml:space="preserve">
    <value>귀하의 이메일을 확인 해 주셔서 감사 합니다. 이제 로그인 자격 증명을 사용할 수 있습니다.</value>
  </data>
  <data name="AccNotActiveForCLient" xml:space="preserve">
    <value>귀하의 계정은 하지 아직 기존 사진 예술 클라이언트에 할당 되었습니다. 기술 지원에 문의 하십시오.</value>
  </data>
  <data name="NothingWasFound" xml:space="preserve">
    <value>아무것도 발견!</value>
  </data>
  <data name="ClientControlPanel" xml:space="preserve">
    <value>클라이언트 컨트롤 패널</value>
  </data>
  <data name="ThankYouForBeingPatient" xml:space="preserve">
    <value>환자가 되 고 주셔서 감사 합니다. 우리는 곧 돌아올 것입니다 사이트에 몇 가지 작업을 하 고 있다.</value>
  </data>
  <data name="UnderConstruction" xml:space="preserve">
    <value>공사중</value>
  </data>
  <data name="SorryWeReDoingSomeWorkOnTheSite" xml:space="preserve">
    <value>죄송 합니다, 우리는 사이트에 몇 가지 작업을 하 고 있어</value>
  </data>
  <data name="Desktop" xml:space="preserve">
    <value>바탕 화면</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>포함</value>
  </data>
  <data name="ProductElements" xml:space="preserve">
    <value>제품 요소</value>
  </data>
  <data name="Specifications" xml:space="preserve">
    <value>구성 요소</value>
  </data>
  <data name="DescriptionRU" xml:space="preserve">
    <value>설명 RU</value>
  </data>
  <data name="DescriptionEN" xml:space="preserve">
    <value>설명 엔</value>
  </data>
  <data name="DescriptionFR" xml:space="preserve">
    <value>설명 FR</value>
  </data>
  <data name="DisabledEntryDesc" xml:space="preserve">
    <value>내보내지지 않습니다 제어판에 밖으로 설정 하는 경우</value>
  </data>
  <data name="UploadFileFieldDesc" xml:space="preserve">
    <value>파일을 업로드 하거나 다음 분야에서 기존 URL을 입력</value>
  </data>
  <data name="NoAction" xml:space="preserve">
    <value>아무 작업도</value>
  </data>
  <data name="OpenProductInApp" xml:space="preserve">
    <value>응용 프로그램에서 오픈 제품</value>
  </data>
  <data name="NavigateToUrl" xml:space="preserve">
    <value>Url로 이동</value>
  </data>
  <data name="FieldMustBeUnique" xml:space="preserve">
    <value>필드 ' {0} ' 값은 고유 해야</value>
  </data>
  <data name="ContentLanguages" xml:space="preserve">
    <value>콘텐츠 언어</value>
  </data>
  <data name="Enter2LettersLanguageCodes" xml:space="preserve">
    <value>2 문자 언어 코드 입력</value>
  </data>
  <data name="Unknown" xml:space="preserve">
    <value>알 수 없는</value>
  </data>
  <data name="CompanyInfo" xml:space="preserve">
    <value>회사 정보</value>
  </data>
  <data name="OrUploadFromDisk" xml:space="preserve">
    <value>수정...</value>
  </data>
  <data name="SortOutDate" xml:space="preserve">
    <value>릴리스 날짜</value>
  </data>
  <data name="SortPriority" xml:space="preserve">
    <value>정렬 우선 순위</value>
  </data>
  <data name="ShowOnPage" xml:space="preserve">
    <value>페이지에 표시</value>
  </data>
  <data name="ExportSection" xml:space="preserve">
    <value>빠른 내보내기</value>
  </data>
  <data name="PlsConfirmExport" xml:space="preserve">
    <value>당신은 지금이 섹션을 수출을 확신 합니까?</value>
  </data>
  <data name="BaseControllerContent__IndexGet_ExportCompletedWithSuccess" xml:space="preserve">
    <value>수출 성공 완료입니다.</value>
  </data>
  <data name="SiteLoading" xml:space="preserve">
    <value>로드</value>
  </data>
  <data name="PushMessages" xml:space="preserve">
    <value>푸시 메시지</value>
  </data>
  <data name="NewsMenu" xml:space="preserve">
    <value>당신의 뉴스</value>
  </data>
  <data name="NavigateToWww" xml:space="preserve">
    <value>응용 프로그램에 내부 url로 이동</value>
  </data>
  <data name="SimpleMessage" xml:space="preserve">
    <value>간단한 메시지</value>
  </data>
  <data name="SendNow" xml:space="preserve">
    <value>지금 보내기</value>
  </data>
  <data name="SaveForLater" xml:space="preserve">
    <value>나중에 저장</value>
  </data>
  <data name="PushEngagedUsers" xml:space="preserve">
    <value>약혼된 사용자</value>
  </data>
  <data name="PushActiveUsers" xml:space="preserve">
    <value>활성 사용자</value>
  </data>
  <data name="PushInactiveUsers" xml:space="preserve">
    <value>비활성 사용자</value>
  </data>
  <data name="OnEditorSubmit_TextCannotBeEmptyForTheEnglishLanguage" xml:space="preserve">
    <value>텍스트 영어 비어 있을 수 없습니다.</value>
  </data>
  <data name="Android" xml:space="preserve">
    <value>안 드 로이드</value>
  </data>
  <data name="AppleIOS" xml:space="preserve">
    <value>애플 iOS</value>
  </data>
  <data name="Dev" xml:space="preserve">
    <value>데 브</value>
  </data>
  <data name="DevicesTotal" xml:space="preserve">
    <value>총 장치</value>
  </data>
  <data name="SavedForLater" xml:space="preserve">
    <value>나중에 저장</value>
  </data>
  <data name="maskSentToDevices" xml:space="preserve">
    <value>{0} 장치에 전송</value>
  </data>
  <data name="PushMessagesWereNotConfigured" xml:space="preserve">
    <value>푸시 메시지 아직 당신을 위해 구성 되지 않았습니다 했다.</value>
  </data>
  <data name="Tenants" xml:space="preserve">
    <value>세 입자</value>
  </data>
  <data name="ClientGlobalSettings" xml:space="preserve">
    <value>클라이언트 전역 설정</value>
  </data>
  <data name="ChangesSaved" xml:space="preserve">
    <value>변경 내용 저장</value>
  </data>
  <data name="NewsController_NewsController_ByRegion" xml:space="preserve">
    <value>디스플레이 지역</value>
  </data>
  <data name="MessagableDevicesTotal" xml:space="preserve">
    <value>Messagable 장치 총</value>
  </data>
  <data name="TotalInstallations" xml:space="preserve">
    <value>총 설치</value>
  </data>
  <data name="CreateAPassword" xml:space="preserve">
    <value>암호 만들기</value>
  </data>
  <data name="YourSecCodeLoginMask" xml:space="preserve">
    <value>보안 코드는 {0}입니다. 예술 사진 컨트롤 패널에 로그인을 사용 합니다.</value>
  </data>
  <data name="IncorrectEmailAddressOrPhoneNumber" xml:space="preserve">
    <value>잘못 된 이메일 주소 또는 전화 번호입니다.</value>
  </data>
  <data name="AUserWithThisPhoneNumberWasNotFoundPleaseRegister" xml:space="preserve">
    <value>이 전화 번호를 가진 사용자 찾을 수 없습니다. 등록 하시기 바랍니다.</value>
  </data>
  <data name="PleaseCheckYouDevice" xml:space="preserve">
    <value>당신이 장치를 확인 하시기 바랍니다</value>
  </data>
  <data name="WeHaveSentAVerificationCodeToYourNumber" xml:space="preserve">
    <value>확인 코드를 귀하의 번호를 발송 하는</value>
  </data>
  <data name="UserWithThisPhoneNumberAlreadyRegistered" xml:space="preserve">
    <value>이 전화 번호를 가진 사용자는 이미 등록.</value>
  </data>
  <data name="WrongCodeEntered" xml:space="preserve">
    <value>잘못 된 코드 입력입니다.</value>
  </data>
  <data name="StatusConfirmed" xml:space="preserve">
    <value>확인</value>
  </data>
  <data name="StatusPendingConfirmation" xml:space="preserve">
    <value>보류 중인 확인</value>
  </data>
  <data name="StatusDisapproved" xml:space="preserve">
    <value>승인</value>
  </data>
  <data name="BookingSystem" xml:space="preserve">
    <value>예약 시스템</value>
  </data>
  <data name="BookingFrontDesk" xml:space="preserve">
    <value>프런트 데스크</value>
  </data>
  <data name="BookingSchedule" xml:space="preserve">
    <value>일정 작업</value>
  </data>
  <data name="BookingRequests" xml:space="preserve">
    <value>예약 요청</value>
  </data>
  <data name="BookingObjects" xml:space="preserve">
    <value>예약 개체</value>
  </data>
  <data name="AddEvent" xml:space="preserve">
    <value>이벤트 추가</value>
  </data>
  <data name="InsertEventName" xml:space="preserve">
    <value>이벤트 이름 삽입</value>
  </data>
  <data name="DragAndDropEventsOnTheCalendar" xml:space="preserve">
    <value>캘린더에 이벤트를 끌어서</value>
  </data>
  <data name="ConnectionError" xml:space="preserve">
    <value>연결 오류</value>
  </data>
  <data name="LatestMobileAppVersion" xml:space="preserve">
    <value>최신 모바일 응용 프로그램 버전</value>
  </data>
  <data name="OutdatedMobileAppVersion" xml:space="preserve">
    <value>오래 된 모바일 응용 프로그램 버전</value>
  </data>
  <data name="StartEvent" xml:space="preserve">
    <value>시작</value>
  </data>
  <data name="EndEvent" xml:space="preserve">
    <value>끝</value>
  </data>
  <data name="AllDay" xml:space="preserve">
    <value>하루 종일</value>
  </data>
  <data name="_2Weeks" xml:space="preserve">
    <value>2 주</value>
  </data>
  <data name="AppoController_Bookable_BlockDayForBooking" xml:space="preserve">
    <value>예약 금지</value>
  </data>
  <data name="AreYouSureToDeleteThisEvent" xml:space="preserve">
    <value>당신은이 이벤트를 삭제 해야 합니까?</value>
  </data>
  <data name="PleaseWait" xml:space="preserve">
    <value>그냥 빠른 초...</value>
  </data>
  <data name="EditEvent" xml:space="preserve">
    <value>이벤트 편집</value>
  </data>
  <data name="EventCard" xml:space="preserve">
    <value>이벤트 카드</value>
  </data>
  <data name="Confirned" xml:space="preserve">
    <value>confirned</value>
  </data>
  <data name="ConfirmationPending" xml:space="preserve">
    <value>보류 중인 확인</value>
  </data>
  <data name="Object" xml:space="preserve">
    <value>개체</value>
  </data>
  <data name="ServicesCategories" xml:space="preserve">
    <value>서비스 카테고리</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>서비스</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>서비스</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>클라이언트</value>
  </data>
  <data name="Details" xml:space="preserve">
    <value>세부 정보</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>전체 이름</value>
  </data>
  <data name="MapX" xml:space="preserve">
    <value>MapX</value>
  </data>
  <data name="KeyHint" xml:space="preserve">
    <value>필요한 고유 키에서 사용 된 모바일 애플 리 케이 션입니다.</value>
  </data>
  <data name="ReservedField" xml:space="preserve">
    <value>사용 되지 않습니다.</value>
  </data>
  <data name="DbNews_UrlProductCodeEtc" xml:space="preserve">
    <value>Url, 제품 코드 ("키" 필드) 등...</value>
  </data>
  <data name="DbNews_WhatToDoWhenNewsFrameIsClickedInApp" xml:space="preserve">
    <value>응용 프로그램에서 뉴스 프레임을 클릭할 때 수행할 작업을</value>
  </data>
  <data name="DbNews_NewsText" xml:space="preserve">
    <value>뉴스 텍스트</value>
  </data>
  <data name="DbNews_LanguageAreaTheNewsWillBeShownIn" xml:space="preserve">
    <value>언어 지역 뉴스에 표시 됩니다.</value>
  </data>
  <data name="DbNews_ImageToBeShownInTheNews" xml:space="preserve">
    <value>뉴스에 표시 되는 이미지.</value>
  </data>
  <data name="InternationalTitlesLanguage" xml:space="preserve">
    <value>국제 타이틀 언어</value>
  </data>
  <data name="PriorityDesc" xml:space="preserve">
    <value>높은 우선 순위 높은 항목에에서 표시 됩니다 목록</value>
  </data>
  <data name="EnabledModules" xml:space="preserve">
    <value>사용 가능한 모듈</value>
  </data>
  <data name="NeedAllUsersRelog" xml:space="preserve">
    <value>사용자 로그 오프</value>
  </data>
  <data name="NeedAllUsersRelogDesc" xml:space="preserve">
    <value>시각적 변경 내용이 적용 되도록이 클라이언트 relog에 관련 된 제어판의 모든 사용자를 확인 합니다.</value>
  </data>
  <data name="HowToUse" xml:space="preserve">
    <value>사용 하는 방법</value>
  </data>
  <data name="RefCodeDesc" xml:space="preserve">
    <value>참조 코드</value>
  </data>
  <data name="TargetPlatfrom" xml:space="preserve">
    <value>플랫폼</value>
  </data>
  <data name="TitleDesc" xml:space="preserve">
    <value>표시 된 제목</value>
  </data>
  <data name="MessageTextDesc" xml:space="preserve">
    <value>메시지 본문</value>
  </data>
  <data name="TargetSegment" xml:space="preserve">
    <value>받는 사람</value>
  </data>
  <data name="TenantNameDesc" xml:space="preserve">
    <value>제어판에서 클라이언트 이름</value>
  </data>
  <data name="Color" xml:space="preserve">
    <value>색</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>가격</value>
  </data>
  <data name="WorkingDays" xml:space="preserve">
    <value>작업 일</value>
  </data>
  <data name="WorkingTimeStart" xml:space="preserve">
    <value>근무 시간에서</value>
  </data>
  <data name="WorkingTimeEnd" xml:space="preserve">
    <value>근무 시간에</value>
  </data>
  <data name="SexRestriction" xml:space="preserve">
    <value>성별 제한</value>
  </data>
  <data name="WorkingTimePauseEnd" xml:space="preserve">
    <value>휴식 끝</value>
  </data>
  <data name="LandingForClients" xml:space="preserve">
    <value>클라이언트</value>
  </data>
  <data name="LandingEnterHere" xml:space="preserve">
    <value>여기에 입력</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>팀</value>
  </data>
  <data name="Goalkeepers" xml:space="preserve">
    <value>골키퍼</value>
  </data>
  <data name="Coaches" xml:space="preserve">
    <value>코치</value>
  </data>
  <data name="Since" xml:space="preserve">
    <value>이후</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>등급</value>
  </data>
  <data name="SortRating" xml:space="preserve">
    <value>평가</value>
  </data>
  <data name="VK" xml:space="preserve">
    <value>VKontakte</value>
  </data>
  <data name="ArenaFeaturesDesc" xml:space="preserve">
    <value>더 많은 정보</value>
  </data>
  <data name="DifficultyLevel" xml:space="preserve">
    <value>난이도</value>
  </data>
  <data name="PriceDetailsDesc" xml:space="preserve">
    <value>"당 시간" 등...</value>
  </data>
  <data name="PriceDetails" xml:space="preserve">
    <value>가격 세부 사항</value>
  </data>
  <data name="WeekDays" xml:space="preserve">
    <value>주 일</value>
  </data>
  <data name="GenerateDropDowns_Unknown" xml:space="preserve">
    <value>알 수 없는</value>
  </data>
  <data name="WorkingTimePauseStart" xml:space="preserve">
    <value>방학 시작</value>
  </data>
  <data name="TimeStart" xml:space="preserve">
    <value>시작 시간</value>
  </data>
  <data name="TimeEnd" xml:space="preserve">
    <value>종료 시간</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>팀</value>
  </data>
  <data name="EventDetails" xml:space="preserve">
    <value>이벤트 세부 정보</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>이벤트</value>
  </data>
  <data name="EventsElements" xml:space="preserve">
    <value>이벤트 요소</value>
  </data>
  <data name="Organizations" xml:space="preserve">
    <value>조직</value>
  </data>
  <data name="Organization" xml:space="preserve">
    <value>조직</value>
  </data>
  <data name="EventType" xml:space="preserve">
    <value>이벤트 유형</value>
  </data>
  <data name="BaseControllerContent_GenerateDropDowns_Rally" xml:space="preserve">
    <value>랠리</value>
  </data>
  <data name="Championship" xml:space="preserve">
    <value>선수권 대회</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>다른</value>
  </data>
  <data name="ScheduleType" xml:space="preserve">
    <value>일정 유형</value>
  </data>
  <data name="ByDaysOfWeek" xml:space="preserve">
    <value>주 일</value>
  </data>
  <data name="WithFixedDate" xml:space="preserve">
    <value>고정된 날짜와</value>
  </data>
  <data name="Schedules" xml:space="preserve">
    <value>일정</value>
  </data>
  <data name="NeedRelogUser" xml:space="preserve">
    <value>사용자를 로그 오프</value>
  </data>
  <data name="BirthDate" xml:space="preserve">
    <value>생년월일</value>
  </data>
  <data name="ValidUsernameRequired" xml:space="preserve">
    <value>유효한 사용자 이름 필요</value>
  </data>
  <data name="WorkingTime" xml:space="preserve">
    <value>근무 시간</value>
  </data>
  <data name="BookingStatus_Unknown" xml:space="preserve">
    <value>알 수 없는</value>
  </data>
  <data name="BookingStatus_Pending" xml:space="preserve">
    <value>BookingStatusPending</value>
  </data>
  <data name="BookingStatus_Confirmed" xml:space="preserve">
    <value>BookingStatusConfirmed</value>
  </data>
  <data name="BookingStatus_Rejected" xml:space="preserve">
    <value>BookingStatusRejected</value>
  </data>
  <data name="BookingStatus_Archived" xml:space="preserve">
    <value>BookingStatusArchived</value>
  </data>
  <data name="BookingRequest" xml:space="preserve">
    <value>예약 요청</value>
  </data>
  <data name="DaysOfWeek_Sunday" xml:space="preserve">
    <value>일요일</value>
  </data>
  <data name="DaysOfWeek_Monday" xml:space="preserve">
    <value>월요일</value>
  </data>
  <data name="DaysOfWeek_Tuesday" xml:space="preserve">
    <value>화요일</value>
  </data>
  <data name="DaysOfWeek_Wednesday" xml:space="preserve">
    <value>수요일</value>
  </data>
  <data name="DaysOfWeek_Thursday" xml:space="preserve">
    <value>목요일</value>
  </data>
  <data name="DaysOfWeek_Friday" xml:space="preserve">
    <value>금요일</value>
  </data>
  <data name="DaysOfWeek_Saturday" xml:space="preserve">
    <value>토요일</value>
  </data>
  <data name="WorkingTimeDetailed" xml:space="preserve">
    <value>근무 시간 Detaled</value>
  </data>
  <data name="AppoConfirmAuto" xml:space="preserve">
    <value>자동 확인 예약</value>
  </data>
  <data name="AppoConfirmAutoDesc" xml:space="preserve">
    <value>자동/수동</value>
  </data>
  <data name="AppoExplicitBookable" xml:space="preserve">
    <value>명시적 예약 시간</value>
  </data>
  <data name="AppoExplicitBookableDesc" xml:space="preserve">
    <value>여부 관광 시간 사용할 수 있도록 각 개체에 대 한 관광 시간에 표시 해야</value>
  </data>
  <data name="btnBook" xml:space="preserve">
    <value>도 서</value>
  </data>
  <data name="Gallery" xml:space="preserve">
    <value>갤러리</value>
  </data>
  <data name="YourName" xml:space="preserve">
    <value>사용자 이름</value>
  </data>
  <data name="BtnBookNow" xml:space="preserve">
    <value>지금 바로 예약 하세요!</value>
  </data>
  <data name="BookOnline" xml:space="preserve">
    <value>온라인 예약</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>돌아 가</value>
  </data>
  <data name="NameTitle" xml:space="preserve">
    <value>제목</value>
  </data>
  <data name="YourFName" xml:space="preserve">
    <value>이름</value>
  </data>
  <data name="YourLName" xml:space="preserve">
    <value>가족 이름</value>
  </data>
  <data name="String" xml:space="preserve">
    <value>문자열</value>
  </data>
  <data name="UpdatingData" xml:space="preserve">
    <value>데이터 업데이트...</value>
  </data>
  <data name="AppoNoTimeDesc" xml:space="preserve">
    <value>주어진된 조건에 대 한 사용할 수 있는 시간이입니다. 아래 조건 변경 시도:</value>
  </data>
  <data name="Oops" xml:space="preserve">
    <value>죄송 합니다!</value>
  </data>
  <data name="Canceled" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="ClientId" xml:space="preserve">
    <value>클라이언트 Id</value>
  </data>
  <data name="AppoTimeDescWho" xml:space="preserve">
    <value>{0} {1}에서 당신을 위해 기다리고 있을 것입니다.</value>
  </data>
  <data name="BookingDateTimeDescFormat" xml:space="preserve">
    <value>우리가 당신을 위해 {0}에서 기다리고 있을 거 야</value>
  </data>
  <data name="AppoTimeDescPending" xml:space="preserve">
    <value>제발 {0}에 대 한 확인을 기다리고 있습니다</value>
  </data>
  <data name="ConfirmationPendingTitle" xml:space="preserve">
    <value>보류 중인 확인</value>
  </data>
  <data name="Image" xml:space="preserve">
    <value>이미지</value>
  </data>
  <data name="PatternUrl" xml:space="preserve">
    <value>PatternUrl</value>
  </data>
  <data name="WallpaperUrl" xml:space="preserve">
    <value>WallpaperUrl</value>
  </data>
  <data name="ControlPanel" xml:space="preserve">
    <value>제어 패널</value>
  </data>
  <data name="AppStrings" xml:space="preserve">
    <value>문자열</value>
  </data>
  <data name="TweakApp" xml:space="preserve">
    <value>팅 겨 보고 모바일</value>
  </data>
  <data name="NoTimeAvailable" xml:space="preserve">
    <value>사용 가능한 시간</value>
  </data>
  <data name="ForBookingOnly" xml:space="preserve">
    <value>만 예약</value>
  </data>
  <data name="Sections" xml:space="preserve">
    <value>섹션</value>
  </data>
  <data name="Article" xml:space="preserve">
    <value>기사</value>
  </data>
  <data name="SeeAlso" xml:space="preserve">
    <value>참고 항목:</value>
  </data>
  <data name="PriceMask" xml:space="preserve">
    <value>가격 마스크</value>
  </data>
  <data name="Appearence" xml:space="preserve">
    <value>외관</value>
  </data>
  <data name="SortNotes" xml:space="preserve">
    <value>노트에 의해</value>
  </data>
  <data name="OurContacts" xml:space="preserve">
    <value>저희에 게 연락</value>
  </data>
  <data name="HowToGet" xml:space="preserve">
    <value>경로 찾기</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>돌아 가</value>
  </data>
  <data name="BookingObjectsShort" xml:space="preserve">
    <value>개체</value>
  </data>
  <data name="ExplainDate_Today" xml:space="preserve">
    <value>오늘</value>
  </data>
  <data name="ExplainDate_Tomm" xml:space="preserve">
    <value>내일</value>
  </data>
  <data name="ExplainDate_X" xml:space="preserve">
    <value>{0} 일</value>
  </data>
  <data name="ExplainDate_X1" xml:space="preserve">
    <value>{0} 일</value>
  </data>
  <data name="ExplainDate_X2" xml:space="preserve">
    <value>{0} 일</value>
  </data>
  <data name="Authenticating" xml:space="preserve">
    <value>인증...</value>
  </data>
  <data name="YouHaveTriedTooManyTimesPleaseTryAgainIn0Mins" xml:space="preserve">
    <value>당신은 너무 많은 시간을 시도, {0} 분에서 다시 시도 하십시오.</value>
  </data>
  <data name="RegistrationFailedPleaseCheckYouHaveProvidedAValidPhoneNumberOrTryAgainLater" xml:space="preserve">
    <value>등록 하지 못했습니다. 유효한 전화 번호를 제공 하거나 나중에 다시 시도 하십시오 확인 하십시오.</value>
  </data>
  <data name="ПроверьтеКорректностьВведенныхДанных" xml:space="preserve">
    <value>입력 한 데이터가 올바른지 확인 하십시오.</value>
  </data>
  <data name="BookingFailed" xml:space="preserve">
    <value>예약 하지 못했습니다.</value>
  </data>
  <data name="VerifyingCode" xml:space="preserve">
    <value>코드를 확인...</value>
  </data>
  <data name="WeHaveSentYouAConfirmationCodeBySMSPleaseEnterItBelowToProcessYourBooking" xml:space="preserve">
    <value>우리가 보낸 당신은 sms 인증 코드. 그것은 귀하의 예약을 처리 하는 데 아래 입력:</value>
  </data>
  <data name="BookingFailedMaybeSomeoneHasAlreadyTakenThatTimePleaseRetry" xml:space="preserve">
    <value>예약 하지 못했습니다. 어쩌면 누군가 이미 당시, 제발 다시 시도.</value>
  </data>
  <data name="FailedToVerifyCode" xml:space="preserve">
    <value>코드를 확인 하지 못했습니다.</value>
  </data>
  <data name="ReloadingBookingData" xml:space="preserve">
    <value>예약 데이터를 다시 로드.</value>
  </data>
  <data name="BookingDateTimeDesc" xml:space="preserve">
    <value>{1}에서 {0}</value>
  </data>
  <data name="CodeFromSMS" xml:space="preserve">
    <value>SMS에서 코드</value>
  </data>
  <data name="BookingFrontDeskStatusType_Confirmed" xml:space="preserve">
    <value>확인</value>
  </data>
  <data name="BookingFrontDeskStatusType_Canceled" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="BookingFrontDeskStatusType_Pending" xml:space="preserve">
    <value>보류 중</value>
  </data>
  <data name="Settings_SelectLanguage" xml:space="preserve">
    <value>언어 선택</value>
  </data>
  <data name="ClickToUploadOrDropFileHere" xml:space="preserve">
    <value>업로드 또는 여기 파일을 클릭 합니다.</value>
  </data>
  <data name="LoadingOriginalImage" xml:space="preserve">
    <value>로드 원본 이미지...</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>보기</value>
  </data>
  <data name="WithoutDescription" xml:space="preserve">
    <value>없이 설명입니다.</value>
  </data>
  <data name="Galleries" xml:space="preserve">
    <value>갤러리</value>
  </data>
  <data name="SystemNameHint" xml:space="preserve">
    <value>모바일 애플 리 케이 션, 시스템 이름 목록 등에서에서이 항목을 선택 하는 데 사용 되는 표시 되지 않습니다</value>
  </data>
  <data name="ExplainDateWithInterval" xml:space="preserve">
    <value>{0}를 기다리고</value>
  </data>
  <data name="BookingTimeDescAt" xml:space="preserve">
    <value>{0}에서</value>
  </data>
  <data name="Blog" xml:space="preserve">
    <value>블로그</value>
  </data>
  <data name="OpenBlogArticle" xml:space="preserve">
    <value>오픈 블로그 기사</value>
  </data>
  <data name="ReleaseDate" xml:space="preserve">
    <value>출시 날짜</value>
  </data>
  <data name="SplashLogo" xml:space="preserve">
    <value>시작 로고</value>
  </data>
  <data name="CompanyLogo" xml:space="preserve">
    <value>회사 이미지</value>
  </data>
  <data name="DisplayedOverOurContacts" xml:space="preserve">
    <value>우리의 연락처에 표시</value>
  </data>
  <data name="Question" xml:space="preserve">
    <value>질문</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>수준</value>
  </data>
  <data name="QuizzQuestionLevel_Easy" xml:space="preserve">
    <value>쉽게</value>
  </data>
  <data name="QuizzQuestionLevel_Normal" xml:space="preserve">
    <value>정상</value>
  </data>
  <data name="QuizzQuestionLevel_Hard" xml:space="preserve">
    <value>하드</value>
  </data>
  <data name="QuizzQuestionLevel_Superhard" xml:space="preserve">
    <value>Superhard</value>
  </data>
  <data name="QuizzQuestionImageType_Normal" xml:space="preserve">
    <value>정상</value>
  </data>
  <data name="QuizzQuestionImageType_Avatar" xml:space="preserve">
    <value>아바타</value>
  </data>
  <data name="Answers" xml:space="preserve">
    <value>답변</value>
  </data>
  <data name="Answer" xml:space="preserve">
    <value>대답</value>
  </data>
  <data name="Correct" xml:space="preserve">
    <value>맞는</value>
  </data>
  <data name="QuizzQuestions" xml:space="preserve">
    <value>Quizz 질문</value>
  </data>
  <data name="SortByLevel" xml:space="preserve">
    <value>수준으로</value>
  </data>
  <data name="QRCodeImageUrl" xml:space="preserve">
    <value>QR 코드 이미지 Url</value>
  </data>
  <data name="Quizz" xml:space="preserve">
    <value>Quizz</value>
  </data>
  <data name="QuestionDurationTime" xml:space="preserve">
    <value>한 가지 질문에 대 한 시간</value>
  </data>
  <data name="Quizzes" xml:space="preserve">
    <value>퀴즈</value>
  </data>
  <data name="QuestionDurationTimeSecs" xml:space="preserve">
    <value>초에 모든 질문에 대 한 시간</value>
  </data>
  <data name="Brands" xml:space="preserve">
    <value>브랜드</value>
  </data>
  <data name="PromoActons" xml:space="preserve">
    <value>프로 모션 해당</value>
  </data>
  <data name="IncludeQuestionsWithTags" xml:space="preserve">
    <value>태그와 함께 포함</value>
  </data>
  <data name="ExcludeQuestionsWithTags" xml:space="preserve">
    <value>태그 제외</value>
  </data>
  <data name="SearchKeywords" xml:space="preserve">
    <value>검색 키워드</value>
  </data>
  <data name="PleaseSaveThisRecordToBeAbleToAddSubRecords" xml:space="preserve">
    <value>하위 레코드를 추가할 수 있도록이 레코드를 저장 하십시오.</value>
  </data>
  <data name="Brand" xml:space="preserve">
    <value>브랜드</value>
  </data>
  <data name="PromoPrizes" xml:space="preserve">
    <value>경품</value>
  </data>
  <data name="CorrectAnswersPercent" xml:space="preserve">
    <value>정답 %</value>
  </data>
  <data name="Discount" xml:space="preserve">
    <value>할인</value>
  </data>
  <data name="PromoAction" xml:space="preserve">
    <value>프로 모션 행동</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>상태</value>
  </data>
  <data name="PromoStatus_Open" xml:space="preserve">
    <value>오픈</value>
  </data>
  <data name="PromoStatus_Closed" xml:space="preserve">
    <value>폐쇄</value>
  </data>
  <data name="PromoStatus_Incoming" xml:space="preserve">
    <value>들어오</value>
  </data>
  <data name="PromoStatus_Other" xml:space="preserve">
    <value>다른</value>
  </data>
  <data name="Exit" xml:space="preserve">
    <value>출구</value>
  </data>
  <data name="SortByStatus" xml:space="preserve">
    <value>상태</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>로드 중입니다...</value>
  </data>
  <data name="ExplainSeconds_0" xml:space="preserve">
    <value>초</value>
  </data>
  <data name="ExplainSeconds_1" xml:space="preserve">
    <value>초</value>
  </data>
  <data name="ExplainSeconds_X1" xml:space="preserve">
    <value>초</value>
  </data>
  <data name="ExplainSeconds_X2" xml:space="preserve">
    <value>초</value>
  </data>
  <data name="ExplainSeconds_X" xml:space="preserve">
    <value>초</value>
  </data>
  <data name="Success_" xml:space="preserve">
    <value>성공</value>
  </data>
  <data name="CouponPercent" xml:space="preserve">
    <value>쿠폰 %</value>
  </data>
  <data name="LinkMoreInfo" xml:space="preserve">
    <value>추가 정보 링크</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>이름</value>
  </data>
  <data name="QuestionsTotal" xml:space="preserve">
    <value>질문 전체 보기</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>가져오기</value>
  </data>
  <data name="IncludeQuestionsWithTagsDesc" xml:space="preserve">
    <value>*-모든 질문 포함 됩니다. 포함 되는 다른 사용자 지정 태그를 지정할 수도 있습니다.</value>
  </data>
  <data name="OpenPromoInApp" xml:space="preserve">
    <value>응용 프로그램에서 열기 promoaction</value>
  </data>
  <data name="MaxPrizes" xml:space="preserve">
    <value>총 상금</value>
  </data>
  <data name="PrizesLeft" xml:space="preserve">
    <value>왼쪽 상품</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>프로 파일</value>
  </data>
  <data name="CustomerConnectResult_Pending" xml:space="preserve">
    <value>보류 중</value>
  </data>
  <data name="CustomerConnectResult_Approved" xml:space="preserve">
    <value>승인</value>
  </data>
  <data name="CustomerConnectResult_Denied" xml:space="preserve">
    <value>거부</value>
  </data>
  <data name="CustomerConnectResult_NetworkError" xml:space="preserve">
    <value>네트워크 오류</value>
  </data>
  <data name="CustomerConnectResult_UnknownError" xml:space="preserve">
    <value>알 수 없는 오류</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>사용자</value>
  </data>
  <data name="TotalConns" xml:space="preserve">
    <value>총 요청</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>요청</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>요청</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>또는</value>
  </data>
  <data name="TotalConnsOk" xml:space="preserve">
    <value>확인된 요청</value>
  </data>
  <data name="CustomerConnectResult_Used" xml:space="preserve">
    <value>만료</value>
  </data>
  <data name="TimeCalculator_Sec" xml:space="preserve">
    <value>s</value>
  </data>
  <data name="TimeCalculator_Min" xml:space="preserve">
    <value>m</value>
  </data>
  <data name="TimeCalculator_Hour" xml:space="preserve">
    <value>h</value>
  </data>
  <data name="UnitsDescMm" xml:space="preserve">
    <value>밀리미터</value>
  </data>
  <data name="UnitsDescInches" xml:space="preserve">
    <value>인치</value>
  </data>
  <data name="UnitsKeyMm" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="UnitsKeyInches" xml:space="preserve">
    <value>에서</value>
  </data>
  <data name="ChooseUnits" xml:space="preserve">
    <value>단위 선택</value>
  </data>
  <data name="MenuPageAbout" xml:space="preserve">
    <value>에 대 한</value>
  </data>
  <data name="MenuPageContacts" xml:space="preserve">
    <value>연락처</value>
  </data>
  <data name="MenuPageNews" xml:space="preserve">
    <value>뉴스</value>
  </data>
  <data name="MenuPageSalons" xml:space="preserve">
    <value>센터 찾기</value>
  </data>
  <data name="PageNewsTitle" xml:space="preserve">
    <value>최신 뉴스</value>
  </data>
  <data name="PageSalonsTitle" xml:space="preserve">
    <value>살롱</value>
  </data>
  <data name="ButtonRegionChange" xml:space="preserve">
    <value>변경 지역</value>
  </data>
  <data name="ButtonNavigate" xml:space="preserve">
    <value>경로 찾기</value>
  </data>
  <data name="Favorite" xml:space="preserve">
    <value>나의 살롱</value>
  </data>
  <data name="PageFindSalon" xml:space="preserve">
    <value>우리의 센터</value>
  </data>
  <data name="ErrorConnRegions" xml:space="preserve">
    <value>연결 오류가 발생 합니다. 나중에 다시 시도 하십시오.</value>
  </data>
  <data name="ErrorConnSalons" xml:space="preserve">
    <value>연결 오류가 발생 합니다. 나중에 다시 시도 하십시오.</value>
  </data>
  <data name="ErrorConnNews" xml:space="preserve">
    <value>연결 오류가 발생 합니다. 나중에 다시 시도 하십시오.</value>
  </data>
  <data name="ErrorConnection" xml:space="preserve">
    <value>연결 오류가 발생 합니다. 인터넷 연결을 확인 하 고 다시 시도 하십시오.</value>
  </data>
  <data name="ButtonOk" xml:space="preserve">
    <value>그래</value>
  </data>
  <data name="PageFavSalon" xml:space="preserve">
    <value>내 즐겨찾기 센터</value>
  </data>
  <data name="FavoriteEmpty1" xml:space="preserve">
    <value>환영 합니다!</value>
  </data>
  <data name="NeedInternet" xml:space="preserve">
    <value>데이터를 로드 하지 못했습니다. 
인터넷 연결을 확인 하십시오.</value>
  </data>
  <data name="ErrorCannotNavigate" xml:space="preserve">
    <value>지도 응용 프로그램을 설치 했는지 확인 하십시오.</value>
  </data>
  <data name="BrowseSite" xml:space="preserve">
    <value>찾아보기 웹사이트</value>
  </data>
  <data name="ShowOnMap" xml:space="preserve">
    <value>우리 지도에 표시</value>
  </data>
  <data name="Call" xml:space="preserve">
    <value>전화</value>
  </data>
  <data name="SaveItToFavorites" xml:space="preserve">
    <value>빠른 액세스를 위해 저장!</value>
  </data>
  <data name="ButtonAddToFavs" xml:space="preserve">
    <value>즐겨찾기에 추가</value>
  </data>
  <data name="ButtonConnect" xml:space="preserve">
    <value>다시 연결</value>
  </data>
  <data name="ButtonHowToGetToUs" xml:space="preserve">
    <value>지침</value>
  </data>
  <data name="AreYouSureRemoveFromFavs" xml:space="preserve">
    <value>정말 마음에 드는 것에서 센터를 제거?</value>
  </data>
  <data name="RemoveFromFavs" xml:space="preserve">
    <value>즐겨찾기에서 제거</value>
  </data>
  <data name="FavDescBlabla" xml:space="preserve">
    <value>이제 즐겨찾기 섹션에서이 센터 데이터를 신속 하 게 액세스할 수 있습니다.</value>
  </data>
  <data name="AboutSalon" xml:space="preserve">
    <value>센터에 대 한</value>
  </data>
  <data name="GPSPermissionsNeedOn" xml:space="preserve">
    <value>우리가 우리를 찾을 수 있을 당신의 GPS 위치를 액세스를 해야 합니다. 지금 액세스를 활성화?</value>
  </data>
  <data name="GPSPleaseTurnOn" xml:space="preserve">
    <value>당신의 GPS 꺼져 있습니다. 제발 켤 우리가 도움을 받을 수 있을.</value>
  </data>
  <data name="HowToGetThereMetroTitle" xml:space="preserve">
    <value>의해서:</value>
  </data>
  <data name="PageContactsInfo" xml:space="preserve">
    <value>정보</value>
  </data>
  <data name="ContactUs" xml:space="preserve">
    <value>저희에 게 연락</value>
  </data>
  <data name="WeOnMap" xml:space="preserve">
    <value>지도에 표시</value>
  </data>
  <data name="Settings_Copyright" xml:space="preserve">
    <value>© 2019 예술 사진 및 해당 콘텐츠 소유자의</value>
  </data>
  <data name="GettingGPSCoords" xml:space="preserve">
    <value>당신의 위치를 찾기...</value>
  </data>
  <data name="PageSalonList" xml:space="preserve">
    <value>목록</value>
  </data>
  <data name="PageSalonListRegion" xml:space="preserve">
    <value>지역</value>
  </data>
  <data name="Facebook" xml:space="preserve">
    <value>페이 스 북</value>
  </data>
  <data name="Instagram" xml:space="preserve">
    <value>Instagram</value>
  </data>
  <data name="ButtonProSalons" xml:space="preserve">
    <value>센터에 대 한 정보</value>
  </data>
  <data name="ButtonProPpl" xml:space="preserve">
    <value>전문가 위한 정보</value>
  </data>
  <data name="ButtonProPartners" xml:space="preserve">
    <value>여기에서 파트너 로그인</value>
  </data>
  <data name="PageHowToGetThereInstructions" xml:space="preserve">
    <value>어떻게 우리를 찾을합니다</value>
  </data>
  <data name="ButtonCancel" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="FavoriteEmpty2" xml:space="preserve">
    <value>빠른 액세스를 위해이 페이지를 좋아하는 THALION 센터를 추가 합니다.</value>
  </data>
  <data name="NavigateTo" xml:space="preserve">
    <value>경로 찾기</value>
  </data>
  <data name="FavReplaceConfirm" xml:space="preserve">
    <value>이 것으로 기존 즐겨찾기를 교체?</value>
  </data>
  <data name="ToSalonList" xml:space="preserve">
    <value>살롱 전체 목록 보기</value>
  </data>
  <data name="km" xml:space="preserve">
    <value>km</value>
  </data>
  <data name="ButtonFindYourSalon" xml:space="preserve">
    <value>살롱 찾기</value>
  </data>
  <data name="FavGratz" xml:space="preserve">
    <value>축 하!</value>
  </data>
  <data name="ButtonGotIt" xml:space="preserve">
    <value>야 호 쿨</value>
  </data>
  <data name="ErrorTitle" xml:space="preserve">
    <value>오류</value>
  </data>
  <data name="ErrorConSalon" xml:space="preserve">
    <value>연결 오류가 발생 합니다. 나중에 다시 시도 하십시오.</value>
  </data>
  <data name="iSalonList" xml:space="preserve">
    <value>센터</value>
  </data>
  <data name="X_AboutUs" xml:space="preserve">
    <value>우리에 관해서</value>
  </data>
  <data name="iRegion" xml:space="preserve">
    <value>지도에</value>
  </data>
  <data name="PageTitleSettings" xml:space="preserve">
    <value>설정</value>
  </data>
  <data name="SettingsInterface" xml:space="preserve">
    <value>인터페이스</value>
  </data>
  <data name="Settings_NoTitlesInTabs" xml:space="preserve">
    <value>아니 텍스트 하단 메뉴</value>
  </data>
  <data name="SettingsStartFav" xml:space="preserve">
    <value>프로그램 시작 페이지 내 살롱 (좋아하는) 표시</value>
  </data>
  <data name="MenuPageHome" xml:space="preserve">
    <value>홈페이지</value>
  </data>
  <data name="SettingsAnimation" xml:space="preserve">
    <value>배터리 절약에 대 한 배경 애니메이션을 사용 하지 않도록 설정</value>
  </data>
  <data name="BackToSalonList" xml:space="preserve">
    <value>다시 목록으로 이동 살롱</value>
  </data>
  <data name="SettingsTutorial" xml:space="preserve">
    <value>프로그램 시작 시 항상 표시 시작 자습서</value>
  </data>
  <data name="MenuSomeMore" xml:space="preserve">
    <value>더욱...</value>
  </data>
  <data name="ShowWelcomeSlides" xml:space="preserve">
    <value>시작 슬라이드 쇼</value>
  </data>
  <data name="StartUp" xml:space="preserve">
    <value>지금 시작</value>
  </data>
  <data name="UpdateNeded" xml:space="preserve">
    <value>우리는 업데이트를 발표 했다, 응용 프로그램을 업데이 트 하시기 바랍니다!</value>
  </data>
  <data name="Bye" xml:space="preserve">
    <value>곧 봐요!</value>
  </data>
  <data name="Settings_SilentPush" xml:space="preserve">
    <value>푸시 메시지 묵</value>
  </data>
  <data name="AskHideWelcome" xml:space="preserve">
    <value>이 환영 메시지를 숨기고 싶은 당신은?</value>
  </data>
  <data name="Tutorial_1_Find" xml:space="preserve">
    <value>찾기</value>
  </data>
  <data name="Tutorial_2_Add" xml:space="preserve">
    <value>설정</value>
  </data>
  <data name="Tutorial_3_Share" xml:space="preserve">
    <value>Сlick 두 번</value>
  </data>
  <data name="Tutorial_4_Follow" xml:space="preserve">
    <value>따라</value>
  </data>
  <data name="Tutorial_3_Share_Desc" xml:space="preserve">
    <value>그것의 뿌리로 돌아가려면 섹션 아이콘</value>
  </data>
  <data name="WebBack" xml:space="preserve">
    <value>뒤로</value>
  </data>
  <data name="SortKm" xml:space="preserve">
    <value>Km로 정렬</value>
  </data>
  <data name="OnMapSalon" xml:space="preserve">
    <value>지도에</value>
  </data>
  <data name="PageSettings_PageSettings_Version" xml:space="preserve">
    <value>버전</value>
  </data>
  <data name="MenuProducts" xml:space="preserve">
    <value>제품 카탈로그</value>
  </data>
  <data name="SubCatsHere" xml:space="preserve">
    <value>하위 카테고리:</value>
  </data>
  <data name="AllProductsHere" xml:space="preserve">
    <value>범주에서 모든 제품</value>
  </data>
  <data name="Conseil" xml:space="preserve">
    <value>THALION 팁</value>
  </data>
  <data name="SearchResults" xml:space="preserve">
    <value>검색 결과</value>
  </data>
  <data name="TapToRead" xml:space="preserve">
    <value>읽기 탭</value>
  </data>
  <data name="SearchProd" xml:space="preserve">
    <value>검색 제품</value>
  </data>
  <data name="EnterString" xml:space="preserve">
    <value>검색</value>
  </data>
  <data name="Popular" xml:space="preserve">
    <value>핫</value>
  </data>
  <data name="Tutorial_5_Products" xml:space="preserve">
    <value>보기</value>
  </data>
  <data name="YouHaveSearched" xml:space="preserve">
    <value>검색 한</value>
  </data>
  <data name="PleaseEnterMoreCharacters" xml:space="preserve">
    <value>더 많은 문자를 입력 하십시오!</value>
  </data>
  <data name="SearchSalonLabel" xml:space="preserve">
    <value>검색 센터</value>
  </data>
  <data name="BtnAppSettings" xml:space="preserve">
    <value>시스템 설정</value>
  </data>
  <data name="ButtonLater" xml:space="preserve">
    <value>나중</value>
  </data>
  <data name="NiftyGPS_AlertGPSisOff_TurnGPSOn" xml:space="preserve">
    <value>GPS를 켜고</value>
  </data>
  <data name="PageSalonList_SortList2_SortedByDistance" xml:space="preserve">
    <value>거리 상으로 분류 되어</value>
  </data>
  <data name="PageSalonList_SortList1_SortedByAlphabet" xml:space="preserve">
    <value>알파벳으로 정렬</value>
  </data>
  <data name="SliderAnnounce" xml:space="preserve">
    <value>핫 제품</value>
  </data>
  <data name="WishListDesc" xml:space="preserve">
    <value>카탈로그에서 제품을 위시 리스트에 추가할 수 있습니다. 
목록은 나중 쇼핑 뷰티 센터 또는 미용사 또는 친구와 함께 공유 하는 데 유용입니다.</value>
  </data>
  <data name="WishListTitle" xml:space="preserve">
    <value>내 위시 리스트</value>
  </data>
  <data name="AboutTheCompany" xml:space="preserve">
    <value>우리에 관해서</value>
  </data>
  <data name="AskForConfirmationWhenRemovingItemFromWishList" xml:space="preserve">
    <value>목록에서 항목을 제거할 때 확인 요청</value>
  </data>
  <data name="OtherCategories" xml:space="preserve">
    <value>다른 카테고리</value>
  </data>
  <data name="GotoProducts" xml:space="preserve">
    <value>카탈로그에가 서</value>
  </data>
  <data name="Поделиться" xml:space="preserve">
    <value>공유</value>
  </data>
  <data name="MenuProductsShort" xml:space="preserve">
    <value>제품</value>
  </data>
  <data name="INTHECATEGORY" xml:space="preserve">
    <value>카테고리 이동</value>
  </data>
  <data name="CardProductFull_SetupCell_Ref" xml:space="preserve">
    <value>참고</value>
  </data>
  <data name="PageWishList_UpdateFavs_ToCatalogue" xml:space="preserve">
    <value>카탈로그</value>
  </data>
  <data name="PageWishList_OnBtnShare_МойСписокЖеланийTHALION" xml:space="preserve">
    <value>내 위시 리스트</value>
  </data>
  <data name="ClearList" xml:space="preserve">
    <value>목록 지우기</value>
  </data>
  <data name="HowToBuyProducts" xml:space="preserve">
    <value>가정 사용을 위해 우리의 제품 인증 센터만 THALION에서 구입하실 수 있습니다.</value>
  </data>
  <data name="HowToBuyNotFound" xml:space="preserve">
    <value>THALION 좋아하는 센터 제품 재고가 원하는 것의 일부를 있지 않다면, 알려주시기 바랍니다 그리고 우리가 구입 당신을 원조 할 것 이다.</value>
  </data>
  <data name="WhereToBuy" xml:space="preserve">
    <value>어디에서 구입</value>
  </data>
  <data name="ContactUs2" xml:space="preserve">
    <value>저희에 게 연락</value>
  </data>
  <data name="CardProductFull_Fav_OnDown_ConfirmFavDelete" xml:space="preserve">
    <value>위시 리스트에서 항목을 제거?</value>
  </data>
  <data name="PageWishList_OnBtnClearList_ConfirmClearList" xml:space="preserve">
    <value>당신은 당신의 위시 리스트를 확신 합니까?</value>
  </data>
  <data name="GPSPleaseTurnOniOS" xml:space="preserve">
    <value>우리는 지리적 위치와 수 있도록 GPS 좌표를 할 것 이다.</value>
  </data>
  <data name="NumDesc_Items_Format" xml:space="preserve">
    <value>당신은 당신의 목록에 {0} {1}.</value>
  </data>
  <data name="NumDesc_Items_0" xml:space="preserve">
    <value>항목</value>
  </data>
  <data name="NumDesc_Items_1" xml:space="preserve">
    <value>항목</value>
  </data>
  <data name="NumDesc_Items_with1" xml:space="preserve">
    <value>항목</value>
  </data>
  <data name="NumDesc_Items_with2" xml:space="preserve">
    <value>항목</value>
  </data>
  <data name="NumDesc_Items_with0" xml:space="preserve">
    <value>항목</value>
  </data>
  <data name="LearnMore" xml:space="preserve">
    <value>더 알아보세요</value>
  </data>
  <data name="ItemAddedToWishList" xml:space="preserve">
    <value>위시 리스트에 추가 된 항목</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>다시 한 번 응용 프로그램을 종료를 다시 눌러</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>리셋</value>
  </data>
  <data name="CatRoot" xml:space="preserve">
    <value>보기 카탈로그</value>
  </data>
  <data name="ToCatRoot" xml:space="preserve">
    <value>요약</value>
  </data>
  <data name="iOSTabsStartup_Setup_WhereToFind" xml:space="preserve">
    <value>어디를 찾아</value>
  </data>
  <data name="PrevCategory" xml:space="preserve">
    <value>이전 카테고리</value>
  </data>
  <data name="NextCategory" xml:space="preserve">
    <value>더 보기...</value>
  </data>
  <data name="SeaAlso" xml:space="preserve">
    <value>참고 항목</value>
  </data>
  <data name="BackToCatalog" xml:space="preserve">
    <value>제품 카탈로그</value>
  </data>
  <data name="iOSTabsStartup_Setup_Favorites" xml:space="preserve">
    <value>즐겨찾기</value>
  </data>
  <data name="iOSTabsStartup_Setup_MyPreferences" xml:space="preserve">
    <value>내 즐겨찾기</value>
  </data>
  <data name="DoYouWantUsToGPS" xml:space="preserve">
    <value>만약 당신이 당신을 찾을 THALION 처럼 센터 가장 가까운 있으십시오 긍정적인 다음 창에.</value>
  </data>
  <data name="Hello" xml:space="preserve">
    <value>안녕하세요</value>
  </data>
  <data name="btnTryAgain" xml:space="preserve">
    <value>다시 한 번 시도해 보세요</value>
  </data>
  <data name="btnCheckSettings" xml:space="preserve">
    <value>설정 확인</value>
  </data>
  <data name="ProcessingYourBooking" xml:space="preserve">
    <value>예약 처리...</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>에 대 한...</value>
  </data>
  <data name="X_TimeCalcShort" xml:space="preserve">
    <value>시간</value>
  </data>
  <data name="X_TimeCalcFull" xml:space="preserve">
    <value>시간 계산기</value>
  </data>
  <data name="X_BellowsShort" xml:space="preserve">
    <value>벨로 우즈</value>
  </data>
  <data name="X_BellowsFull" xml:space="preserve">
    <value>벨로 확장</value>
  </data>
  <data name="X_FocalLength" xml:space="preserve">
    <value>초점 거리</value>
  </data>
  <data name="X_BellowsDesc" xml:space="preserve">
    <value>여기 당신의 f/정지는 벨로 우즈 카메라를 사용 하 여 설정에 대 한 정확한 값을 얻을 수 있습니다.</value>
  </data>
  <data name="Millisecs" xml:space="preserve">
    <value>ms</value>
  </data>
  <data name="NumericDoubleDot" xml:space="preserve">
    <value>.</value>
  </data>
  <data name="OfflineCompanyDesc" xml:space="preserve">
    <value>예술 사진 프로젝트 2011의 시작 부분에 설립 되었습니다. 갤러리 뒤에 아이디어는 러시아의 사진 유산을 보존 하 고, 그것을 개발, 둘 다 예술 및 기술적 도움말 현대 아날로그 사진도 지원 하기 위해 같은 시간에. 우리 오픈 아트의 사진 갤러리, 암실 및 대형 사진 스튜디오 세인트 피터 스 버그에서 2015 년에.

사진 갤러리의 예술 역사 및 예술적인 가치를가지고 사진 컬렉션입니다. 지금이 순간, 우리는 발레리 Plotnikov, Boris Smelov, 존 섹스 턴, 레오니트 Bogdanov, 발렌틴 Samarin, 존 Wimberly, 로버트 Doisneau 등 같은 잘 알려진 마스터 제작한 작품.

갤러리의 주요 목표 둘 다 국내외에서 모두 재능 있는 러시아 사진 작가 홍보 하는 것입니다. 우리는 적극적으로 상트페테르부르크와 모스크바 그의 작품은 러시아의 예술적 유산에 추가 될 가능성이 있는 사진 작가 지원 합니다. 

예술 사진 작가 ' 연방의 멤버 구성의 연례 전시회 전통과 현대 러시아 사진 감정가 및 전문가 worlwide 중의 긍정적인 이미지를 만드는 겨냥 하는 유럽과 러시아에서 흑인과 백인 사진 인쇄.</value>
  </data>
  <data name="OfflineCompanyAddress" xml:space="preserve">
    <value>Bolshaya Konyushennaya 거리, 1, 191186-상트페테르부르크, 러시아</value>
  </data>
  <data name="OfflineMapDesc" xml:space="preserve">
    <value>환영 합니다</value>
  </data>
  <data name="Collapse" xml:space="preserve">
    <value>축소</value>
  </data>
  <data name="Expand" xml:space="preserve">
    <value>확장</value>
  </data>
  <data name="HelpCalculator" xml:space="preserve">
    <value>C-보도 한 번 두 번 전체 리셋에 대 한 현재 값을 재설정
%-이전에 입력 한 작업에 사용 됩니다.
예: 프레스 + %10 진수 숫자를 입력 합니다.
결과: 기존에 입력 한 백분율을 추가 했습니다.</value>
  </data>
  <data name="X_BellowsHelp" xml:space="preserve">
    <value>입력 필드의 오른쪽에 해당 텍스트를 클릭 하 여 mm 및 인치 사이 전환할 수 있습니다.</value>
  </data>
  <data name="X_EnableSound" xml:space="preserve">
    <value>사운드</value>
  </data>
  <data name="X_EnableHoursInput" xml:space="preserve">
    <value>시간 입력 사용</value>
  </data>
  <data name="X_TimerStartedAt" xml:space="preserve">
    <value>타이머를 {0}에서 시작</value>
  </data>
  <data name="X_TimerFinishedFor" xml:space="preserve">
    <value>타이머를 {0}에 대 한 완료</value>
  </data>
  <data name="X_35mmHelp" xml:space="preserve">
    <value>입력 필드의 오른쪽에 해당 텍스트를 클릭 하 여 mm 및 인치 사이 전환할 수 있습니다.</value>
  </data>
  <data name="X_DeveloperHelp" xml:space="preserve">
    <value>이것은 개발자 모듈 도움말입니다.</value>
  </data>
  <data name="Parts" xml:space="preserve">
    <value>부품</value>
  </data>
  <data name="Milliliters" xml:space="preserve">
    <value>ml</value>
  </data>
  <data name="X_DeveloperShort" xml:space="preserve">
    <value>개발자</value>
  </data>
  <data name="X_DeveloperFull" xml:space="preserve">
    <value>개발자를 혼합</value>
  </data>
  <data name="X_35MmShort" xml:space="preserve">
    <value>35 m m</value>
  </data>
  <data name="X_35mmFull" xml:space="preserve">
    <value>35 m m 변환</value>
  </data>
  <data name="X_35mmDesc" xml:space="preserve">
    <value>여기 얻을 수 있습니다 35mm에 표현 하 여 렌즈의 초점 거리에 해당.</value>
  </data>
  <data name="X_FrameFormat" xml:space="preserve">
    <value>프레임 형식:</value>
  </data>
  <data name="X_WithinVolume" xml:space="preserve">
    <value>볼륨 내에서</value>
  </data>
  <data name="X_FromGiven" xml:space="preserve">
    <value>주어진</value>
  </data>
  <data name="X_ResultMl" xml:space="preserve">
    <value>결과 (ml)</value>
  </data>
  <data name="X_SolutionA" xml:space="preserve">
    <value>부품 A</value>
  </data>
  <data name="X_SolutionB" xml:space="preserve">
    <value>파트 B</value>
  </data>
  <data name="X_Water" xml:space="preserve">
    <value>물</value>
  </data>
  <data name="X_DeveloperDescA" xml:space="preserve">
    <value>지정 된 볼륨에 따라 개발자의 구성 요소의 계산.</value>
  </data>
  <data name="X_DeveloperDescB" xml:space="preserve">
    <value>지정 된 양의 개발자를 볼륨의 자동 선택.</value>
  </data>
  <data name="X_35mmResult" xml:space="preserve">
    <value>변환</value>
  </data>
  <data name="X_BellowsResult" xml:space="preserve">
    <value>f/정지</value>
  </data>
  <data name="X_BellowsResultDesc" xml:space="preserve">
    <value>당신의 결과 {0:0.00}</value>
  </data>
  <data name="Settings_ChooseYourTabsMinMax" xml:space="preserve">
    <value>탭을 선택 하십시오 ({0} / {1} 분 {2})</value>
  </data>
  <data name="Settings_FavsTabs" xml:space="preserve">
    <value>표시 탭을 선택</value>
  </data>
  <data name="Settings_SelectTheme" xml:space="preserve">
    <value>테마</value>
  </data>
  <data name="X_ThemeDark" xml:space="preserve">
    <value>어두운</value>
  </data>
  <data name="X_ThemeLight" xml:space="preserve">
    <value>빛</value>
  </data>
  <data name="X_AboutFooter" xml:space="preserve">
    <value>AppoMobi에 의해 개발 된 응용 프로그램</value>
  </data>
  <data name="X_35mmResultDesc" xml:space="preserve">
    <value>K = {0}, 대각선은 {1}.

{2}</value>
  </data>
  <data name="X_SolutionResult" xml:space="preserve">
    <value>준비 솔루션</value>
  </data>
  <data name="AskForRating_Question" xml:space="preserve">
    <value>{0} 즐기는?</value>
  </data>
  <data name="AskForRating_ThanksForNegative" xml:space="preserve">
    <value>귀하의 의견 주셔서 감사 합니다, 우리는 우리의 응용 프로그램을 개선 하기 위해 시도할 것 이다!</value>
  </data>
  <data name="AskForRating_GooglePlay" xml:space="preserve">
    <value>GooglePlay에서 우리를 평가 해 주셔서 감사 합니다, 우리는 매우 감사 될 것 이다!</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>필수</value>
  </data>
  <data name="X_NoFilter" xml:space="preserve">
    <value>필터 없음</value>
  </data>
  <data name="Overflow" xml:space="preserve">
    <value>오버플로</value>
  </data>
  <data name="X_Mins" xml:space="preserve">
    <value>분</value>
  </data>
  <data name="X_Secs" xml:space="preserve">
    <value>초</value>
  </data>
  <data name="TimeCalculator_Day" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="TestOne" xml:space="preserve">
    <value>몇 가지 물건을 먹으십시오</value>
  </data>
  <data name="ScheduleTypeDesc" xml:space="preserve">
    <value>고정된 날짜와 요일 사용 되지 않습니다 반대로.</value>
  </data>
  <data name="X_UnknownFormula" xml:space="preserve">
    <value>포뮬러는 생산자에 의해 전달 되지...</value>
  </data>
  <data name="X_DevelopmentUnrecommended" xml:space="preserve">
    <value>이 개발 기간 제조 업체에서 권장 하지 않습니다.</value>
  </data>
  <data name="X_ReciprocityHint" xml:space="preserve">
    <value>슈바르츠 실 트 효과 ("Reciprocity effect")의 영향을 고려 하는 노출의 계산</value>
  </data>
  <data name="X_Reciprocity" xml:space="preserve">
    <value>슈바르츠</value>
  </data>
  <data name="X_ReciprocityHelp" xml:space="preserve">
    <value>경고

수정 필터 제조 업체 및 종류에 의해 촬영의 값 평균 조명. 

필터와 안정적이 고 정확한 결과 얻기 위해 영화를 테스트 하는 것이 좋습니다.</value>
  </data>
  <data name="X_OwnFormula" xml:space="preserve">
    <value>우리 자신의 수식을 사용 하 여 제조 업체의 데이터의 부재로 인해</value>
  </data>
  <data name="X_Unneeded" xml:space="preserve">
    <value>생산자에 따르면 수정이이 피치에 필요 하지 않습니다.</value>
  </data>
  <data name="X_OurNews" xml:space="preserve">
    <value>우리의 뉴스</value>
  </data>
  <data name="X_NotesKodak3200" xml:space="preserve">
    <value>2002 데이터
제조 업체에 따라 그것은 우리가 우리 자신의 수식을 사용 하 여 1 초 이상에 대 한 두 번째, 아래 조정을 필요가 없습니다.</value>
  </data>
  <data name="FilmNotes_Kodak" xml:space="preserve">
    <value>2016 년까지 데이터
제조 업체 개발에는 보정을 권장 합니다.
02시 %
&amp;gt; 50 초:-20%
&amp;gt; 20 분:-30%</value>
  </data>
  <data name="CameraFull" xml:space="preserve">
    <value>네거티브 카메라</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>카메라</value>
  </data>
  <data name="PermissionsError" xml:space="preserve">
    <value>이 모듈은 권한없이 작동 할 수 없습니다. 시스템 설정에서 앱을 승인하거나 앱을 제거하고 처음부터 설치하여 시스템 권한 요청을 다시 받으십시오.</value>
  </data>
  <data name="NoPermissions" xml:space="preserve">
    <value>권한 없음</value>
  </data>
  <data name="Viewfinder" xml:space="preserve">
    <value>뷰 파인더</value>
  </data>
  <data name="ViewfinderFull" xml:space="preserve">
    <value>뷰 파인더</value>
  </data>
  <data name="Selection" xml:space="preserve">
    <value>선택</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>대다</value>
  </data>
  <data name="LensesFor" xml:space="preserve">
    <value>"{0}"용 렌즈</value>
  </data>
  <data name="ChangeFormat" xml:space="preserve">
    <value>형식 변경</value>
  </data>
  <data name="EditPresets" xml:space="preserve">
    <value>사전 설정 편집</value>
  </data>
  <data name="Preset" xml:space="preserve">
    <value>사전 설정</value>
  </data>
  <data name="Films" xml:space="preserve">
    <value>영화</value>
  </data>
  <data name="Filters" xml:space="preserve">
    <value>필터</value>
  </data>
  <data name="CameraZoomHelp" xml:space="preserve">
    <value>이 모듈은 아날로그 뷰 파인더의 대략적인 시뮬레이션을 위해 설계되었습니다. 손가락으로 화면을 확대 할 수 있습니다. 녹색 값을 탭할 수 있습니다.</value>
  </data>
  <data name="NoLensAdded" xml:space="preserve">
    <value>추가 된 렌즈 없음</value>
  </data>
  <data name="Format" xml:space="preserve">
    <value>체재</value>
  </data>
  <data name="AddLens" xml:space="preserve">
    <value>렌즈 추가 (mm)</value>
  </data>
  <data name="OptionScreenOn" xml:space="preserve">
    <value>화면 항상 켜기</value>
  </data>
  <data name="Adjustment" xml:space="preserve">
    <value>조정</value>
  </data>
  <data name="X_NeedMoreForGeo" xml:space="preserve">
    <value>위치 태그가 지정된 사진에 필요한 권한</value>
  </data>
  <data name="X_OptionSpecialCameraFolder" xml:space="preserve">
    <value>Art Of Foto 폴더를 사용하세요</value>
  </data>
  <data name="BtnOpen" xml:space="preserve">
    <value>열려 있는</value>
  </data>
  <data name="X_OptionUseGeo" xml:space="preserve">
    <value>지오 태그 사진</value>
  </data>
  <data name="Reconnect" xml:space="preserve">
    <value>다시 연결</value>
  </data>
  <data name="LightPad" xml:space="preserve">
    <value>개발 테이블</value>
  </data>
  <data name="LightPadShort" xml:space="preserve">
    <value>개발 테이블</value>
  </data>
  <data name="Exposure" xml:space="preserve">
    <value>박람회</value>
  </data>
  <data name="Aperture" xml:space="preserve">
    <value>구멍</value>
  </data>
  <data name="Shutter" xml:space="preserve">
    <value>셔터</value>
  </data>
  <data name="Retry" xml:space="preserve">
    <value>다시</value>
  </data>
  <data name="SpotMeter" xml:space="preserve">
    <value>스팟 측광</value>
  </data>
  <data name="SpotMeterShort" xml:space="preserve">
    <value>스팟 측광</value>
  </data>
  <data name="Correction" xml:space="preserve">
    <value>보정</value>
  </data>
  <data name="LightMeter" xml:space="preserve">
    <value>노출 미터</value>
  </data>
  <data name="LightMeterShort" xml:space="preserve">
    <value>노출 미터</value>
  </data>
  <data name="Timer Short" xml:space="preserve">
    <value>시간제 노동자</value>
  </data>
  <data name="Timer" xml:space="preserve">
    <value>시간제 노동자</value>
  </data>
  <data name="HelpLightPad" xml:space="preserve">
    <value>예를 들어, 더 많은 전문적인 장치가 없을 때 분뇨의 품질을 평가하기위한 필름과 같은 루멘에서 투명한 재료를 볼 수있는 가벼운 테이블.</value>
  </data>
  <data name="HelpExposure" xml:space="preserve">
    <value>장치 카메라의 데이터를 기반으로 한 노출 측정.</value>
  </data>
  <data name="HelpSpot" xml:space="preserve">
    <value>뾰족한 빛 얼 냉동 (반사 된 빛의 노출).</value>
  </data>
  <data name="HelpTimer" xml:space="preserve">
    <value>분뇨 및 사진 인쇄를위한 타이머. 장치에 의해 방사 된 빛은 빛으로 이어지고 화면의 밝기를 최소로 줄이며 사용하기 전에 감광성 재료를 테스트해야합니다.</value>
  </data>
  <data name="WarningTimer" xml:space="preserve">
    <value>개발 프로세스의 모든 단계는 설정에서 편집 할 수 있습니다.</value>
  </data>
  <data name="WarningSpot" xml:space="preserve">
    <value>계산 된 박람회는 참조 및 근사치 자연이며 장치 카메라의 데이터를 기반으로 전문가 전시 업체를 사용하는 정확한 측정을 위해 근사합니다.</value>
  </data>
  <data name="WarningExposure" xml:space="preserve">
    <value>계산 된 박람회는 EXIF의 데이터를 기반으로 참조 및 대략적인 자연이며 정확한 측정을 위해 전문 전시 업체를 사용합니다.</value>
  </data>
  <data name="CameraHelp" xml:space="preserve">
    <value>카메라는 부정적인 시간을보고 BB/색상을 변경하고 프레임을 갤러리에 저장할 수 있습니다.</value>
  </data>
  <data name="X_AdjustedTime" xml:space="preserve">
    <value>셔터</value>
  </data>
  <data name="EditStep" xml:space="preserve">
    <value>편집</value>
  </data>
  <data name="NewStep" xml:space="preserve">
    <value>새로운 단계</value>
  </data>
  <data name="TimerStepSoak" xml:space="preserve">
    <value>담그다</value>
  </data>
  <data name="TimerStepDeveloper" xml:space="preserve">
    <value>표명</value>
  </data>
  <data name="TimerStopBath" xml:space="preserve">
    <value>멈추다</value>
  </data>
  <data name="TimerStepFixer" xml:space="preserve">
    <value>고정</value>
  </data>
  <data name="TimerStepWash" xml:space="preserve">
    <value>세탁</value>
  </data>
  <data name="SkinRed" xml:space="preserve">
    <value>빨간색</value>
  </data>
  <data name="SkinDefault" xml:space="preserve">
    <value>정상</value>
  </data>
  <data name="Skin" xml:space="preserve">
    <value>테마</value>
  </data>
  <data name="Steps" xml:space="preserve">
    <value>단계</value>
  </data>
</root>