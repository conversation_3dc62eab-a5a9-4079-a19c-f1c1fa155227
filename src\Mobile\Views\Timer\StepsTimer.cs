﻿using System.Diagnostics;
using AppoMobi.Maui.DrawnUi.Demo.Views.Controls;
using AppoMobi.Models;
using AppoMobi.Xam;
using DrawnUi.Controls;

namespace AppoMobi.Main
{
    public class StepsTimer : AppScreen
    {
        public List<TimerStep> AvailableTimers { get; } = new();

        public List<RunningTimer> Timers { get; } = new();

        private RunningTimer _currentTimer = new();
        public RunningTimer CurrentTimer
        {
            get => _currentTimer;
            set
            {
                if (_currentTimer != value)
                {
                    _currentTimer = value;
                    OnPropertyChanged();
                }
            }
        }

        private int visibleScreen = 0;
        /// <summary>
        /// Controls which sub-screen will be visible, by index
        /// </summary>
        public int VisibleScreen
        {
            get => visibleScreen;
            set
            {
                if (value == visibleScreen)
                {
                    return;
                }

                visibleScreen = value;
                OnPropertyChanged();
                Update();
            }
        }

        public override void OnWillDisposeWithChildren()
        {
            base.OnWillDisposeWithChildren();

            StopTimer();
            RadioButtons.All.Changed -= OnRadioChanged;
        }

        public StepsTimer()
        {
            BackgroundColor = Colors.Black;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            // Load saved timers or use preset
            var savedTimers = TimerStep.Load();
            if (savedTimers != null && savedTimers.Count > 0)
            {
                AvailableTimers.AddRange(savedTimers);
            }
            else
            {
                // Use preset if no saved timers
                AvailableTimers.AddRange(new List<TimerStep>()
                {
                    new() { Title =ResStrings.TimerStepSoak, Time = new TimeSpan(0, 3, 0), },
                    new() { Title = ResStrings.TimerStepDeveloper, Time = new TimeSpan(0, 12, 0), },
                    new() { Title = ResStrings.TimerStopBath, Time = new TimeSpan(0, 1, 0), },
                    new() { Title = ResStrings.TimerStepFixer, Time = new TimeSpan(0, 5, 0), },
                    new() { Title = ResStrings.TimerStepWash, Time = new TimeSpan(0, 20, 0), },
                });
            }

            foreach (TimerStep availableTimer in AvailableTimers)
            {
                Timers.Add(new RunningTimer() { Timer = availableTimer });
            }

            if (Timers.Count > 0)
            {
                CurrentTimer = Timers[0];
                // Initialize the current timer's time display
                CurrentTimer.Time = CurrentTimer.Timer.Time;
                CurrentTimer.Progress = 0;
            }

            InitPickers();

            RadioButtons.All.Changed += OnRadioChanged;

            var skin = Preferences.Get("TimerSkin", 0);
           SelectSkin(skin);
        }

        #region TIMER

        private System.Timers.Timer _countdownTimer;
        private DateTime _startTime;
        private TimeSpan _remainingTime;
        private bool _isRunning;
        private bool _isPaused;

        public bool IsRunning
        {
            get => _isRunning;
            set
            {
                if (_isRunning != value)
                {
                    _isRunning = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StartButtonText));
                }
            }
        }

        public bool IsPaused
        {
            get => _isPaused;
            set
            {
                if (_isPaused != value)
                {
                    _isPaused = value;
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StartButtonText));
                }
            }
        }

        public string StartButtonText
        {
            get
            {
                if (IsRunning || IsPaused)
                    return "STOP";
                else
                    return "START";
            }
        }

        void StartTimer()
        {
            if (CurrentTimer?.Timer == null) return;

            if (IsPaused)
            {
                // Resume from pause
                _startTime = DateTime.Now - (CurrentTimer.Timer.Time - _remainingTime);
                IsPaused = false;
            }
            else
            {
                // Fresh start
                _startTime = DateTime.Now;
                _remainingTime = CurrentTimer.Timer.Time;
                CurrentTimer.Time = CurrentTimer.Timer.Time;
                CurrentTimer.Progress = 0;
            }

            IsRunning = true;

            _countdownTimer?.Stop();
            _countdownTimer = new System.Timers.Timer(100); // Update every 100ms for smooth progress
            _countdownTimer.Elapsed += OnTimerTick;
            _countdownTimer.Start();
        }

        void StopTimer()
        {
            _countdownTimer?.Stop();
            _countdownTimer?.Dispose();
            _countdownTimer = null;

            IsRunning = false;
            IsPaused = false;

            // Reset to original time
            if (CurrentTimer?.Timer != null)
            {
                CurrentTimer.Time = CurrentTimer.Timer.Time;
                CurrentTimer.Progress = 0;
            }
        }

        void PauseTimer()
        {
            if (!IsRunning) return;

            _countdownTimer?.Stop();
            IsPaused = true;
            IsRunning = false;
        }

        private void OnTimerTick(object sender, System.Timers.ElapsedEventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                if (CurrentTimer?.Timer == null) return;

                var elapsed = DateTime.Now - _startTime;
                _remainingTime = CurrentTimer.Timer.Time - elapsed;

                if (_remainingTime <= TimeSpan.Zero)
                {
                    // Timer finished
                    _remainingTime = TimeSpan.Zero;
                    CurrentTimer.Time = TimeSpan.Zero;
                    CurrentTimer.Progress = 100;

                    StopTimer();
                    OnTimerCompleted();
                }
                else
                {
                    // Update current timer
                    CurrentTimer.Time = _remainingTime;
                    var totalSeconds = CurrentTimer.Timer.Time.TotalSeconds;
                    var remainingSeconds = _remainingTime.TotalSeconds;
                    CurrentTimer.Progress = ((totalSeconds - remainingSeconds) / totalSeconds) * 100;
                }
            });
        }

        private void OnTimerCompleted()
        {
            // Timer completed - could add sound notification, vibration, etc.
            Debug.WriteLine($"Timer '{CurrentTimer.Timer.Title}' completed!");

            // Auto-advance to next timer if available
            var currentIndex = Timers.IndexOf(CurrentTimer);
            if (currentIndex >= 0 && currentIndex < Timers.Count - 1)
            {
                CurrentTimer = Timers[currentIndex + 1];
                // Optionally auto-start next timer
                // StartTimer();
            }
        }



        #endregion

        #region UI

        public Color TextColor = Colors.Red;

        private double StrokeWidth = 1.0;

        void SelectSkin(int skin)
        {
            if (skin == 1)
            {
                //red
                TextColor = Colors.Red;
            }
            else
            {
                //default
                TextColor = Colors.White;
            }
            Preferences.Set("TimerSkin", skin);
            Build();
        }

        public void Build()
        {
            var pickerGradient = new SkiaGradient()
            {
                StartXRatio = 0,
                EndXRatio = 0,
                StartYRatio = 0,
                EndYRatio = 1,
                Colors = new Color[]
                    {
                        TextColor.WithAlpha(0.05f), TextColor.WithAlpha(0.1f), TextColor.WithAlpha(0.05f),
                    },
                ColorPositions = new List<double>() { 0, 0.5, 1 }
            };

            Children = new List<SkiaControl>()
            {
                // TIMER SCREEN
                new SkiaLayer()
                {
                    Tag = "TIMER-0",
                    VerticalOptions = LayoutOptions.Fill,
                    Type = LayoutType.Column,
                    Padding = new(16, 24, 16, 24),
                    HorizontalOptions = LayoutOptions.Fill,
                    Children = new List<SkiaControl>()
                    {
                        new SkiaLabel()
                        {
                            Text = $"{CurrentTimer.Timer.Title}",
                            HorizontalOptions = LayoutOptions.Fill,
                            HorizontalTextAlignment = DrawTextAlignment.Center,
                            FontSize = 24,
                            MaxLines = 1,
                            TextColor = TextColor,
                            FontFamily = "FontTextTitle"
                        },
                        new SkiaLabel()
                        {
                            Text = $"{CurrentTimer.Time:mm\\:ss}",
                            HorizontalOptions = LayoutOptions.Center,
                            MonoForDigits = "8",
                            FontSize = 64,
                            TextColor = TextColor,
                            FontFamily = "FontTextTitle"
                        },
                        new SkiaProgress()
                        {
                            Value = CurrentTimer.Progress,
                            Margin = 32,
                            HeightRequest = 24,
                            HorizontalOptions = LayoutOptions.Fill,
                            ProgressColor = TextColor,
                            TrackColor = TextColor.WithAlpha(0.33f),
                            BackgroundColor = Colors.Transparent,
                        },
                        new SkiaLayer()
                        {
                            VerticalOptions = LayoutOptions.Fill,
                            Children =
                            {
                                new SkiaStack()
                                {
                                    VerticalOptions = LayoutOptions.End,
                                    ItemsSource = Timers,
                                    ItemTemplate = new DataTemplate(() =>
                                    {
                                        SkiaLabel cellTitle;
                                        SkiaLabel cellTime;
                                        SkiaProgress cellProgress;
                                        var cell = new SkiaGrid()
                                            {
                                                ColumnSpacing = 8,
                                                Children =
                                                {
                                                    new SkiaMarkdownLabel()
                                                        {
                                                            TextColor = TextColor,
                                                            FontFamily = "FontTextTitle",
                                                            HorizontalOptions = LayoutOptions.End
                                                        }
                                                        .Assign(out cellTitle).WithColumn(0),
                                                    new SkiaMarkdownLabel()
                                                        {
                                                            TextColor = TextColor, FontFamily = "FontTextTitle"
                                                        }
                                                        .Assign(out cellTime).WithColumn(1),
                                                    new SkiaProgress()
                                                    {
                                                        VerticalOptions = LayoutOptions.Center,
                                                        ProgressColor = TextColor,
                                                        TrackColor = TextColor.WithAlpha(0.66f)
                                                    }.Assign(out cellProgress).WithColumn(2)
                                                }
                                            }.WithColumnDefinitions("*,Auto,80")
                                            .ObserveSelf((me, prop) =>
                                            {
                                                if (me.BindingContext is RunningTimer ctx)
                                                {
                                                    if (prop.IsEither(nameof(BindingContext)))
                                                    {
                                                        cellProgress.Value = ctx.Progress;
                                                        cellTime.Text = $"{ctx.Time:mm\\:ss}";
                                                        cellTitle.Text = ctx.Timer.Title;
                                                    }
                                                    else if (prop.IsEither(nameof(RunningTimer.Progress)))
                                                    {
                                                        cellProgress.Value = ctx.Progress;
                                                    }
                                                }
                                            });
                                        return cell;
                                    })
                                },
                            }
                        },

                        //BUTTONS START ETC
                        new SkiaGrid()
                        {
                            HorizontalOptions = LayoutOptions.Fill,
                            Margin = 24,
                            ColumnSpacing = 16,
                            Children =
                            {
                                new SkiaShape()
                                {
                                    HorizontalOptions = LayoutOptions.End,
                                    StrokeColor = TextColor,
                                    StrokeWidth = StrokeWidth,
                                    VerticalOptions = LayoutOptions.Start,
                                    UseCache = SkiaCacheType.Image,
                                    CornerRadius = 12,
                                    HeightRequest = 38,
                                    WidthRequest = 150,
                                    BackgroundColor = Colors.Transparent,
                                    Children =
                                    {
                                        new SkiaMarkdownLabel(ResStrings.Settings)
                                        {
                                            HorizontalOptions = LayoutOptions.Center,
                                            VerticalOptions = LayoutOptions.Center,
                                            MaxLines = 1,
                                            FontSize = 14,
                                            TextColor = TextColor,
                                            FontFamily = "FontTextTitle"
                                        }
                                    }
                                }.OnTapped((me) =>
                                {
                                    Debug.WriteLine("Settings");
                                    VisibleScreen = 1;
                                }),

                                // START
                                new SkiaShape()
                                {
                                    StrokeColor = TextColor,
                                    StrokeWidth = StrokeWidth,
                                    VerticalOptions = LayoutOptions.Start,
                                    UseCache = SkiaCacheType.Image,
                                    CornerRadius = 12,
                                    HeightRequest = 38,
                                    WidthRequest = 150,
                                    BackgroundColor = Colors.Transparent,
                                    Children =
                                    {
                                        new SkiaMarkdownLabel()
                                        {
                                            HorizontalOptions = LayoutOptions.Center,
                                            VerticalOptions = LayoutOptions.Center,
                                            MaxLines = 1,
                                            FontSize = 14,
                                            TextColor = TextColor,
                                            FontFamily = "FontTextTitle"
                                        }.BindProperty(SkiaMarkdownLabel.TextProperty, this, nameof(StartButtonText))
                                    }
                                }.OnTapped((me) =>
                                {
                                    if (IsRunning || IsPaused)
                                    {
                                        StopTimer();
                                    }
                                    else
                                    {
                                        StartTimer();
                                    }
                                }).WithColumn(1),
                            }
                        }.WithColumnDefinitions("*,Auto,*")
                    }
                }.Observe(this, (me, prop) =>
                {
                    if (prop.IsEither(nameof(BindingContext), nameof(VisibleScreen)))
                    {
                        me.IsVisible = VisibleScreen == 0;
                    }
                }),

                //SETTINGS SCREEN
                new SkiaLayer()
                {
                    Tag = "Settings-1",
                    VerticalOptions = LayoutOptions.Fill,
                    Type = LayoutType.Column,
                    Spacing = 24,
                    Padding = new(16, 24, 16, 24),
                    HorizontalOptions = LayoutOptions.Fill,
                    Children = new List<SkiaControl>()
                    {
                        new SkiaMarkdownLabel(ResStrings.Settings)
                        {
                            HorizontalOptions = LayoutOptions.Center,
                            FontSize = 24,
                            MaxLines = 1,
                            TextColor = TextColor,
                            FontFamily = "FontTextTitle"
                        },
                        new SkiaMarkdownLabel(ResStrings.Skin)
                        {
                            HorizontalOptions = LayoutOptions.Center,
                            FontSize = 18,
                            MaxLines = 1,
                            TextColor = TextColor,
                            FontFamily = "FontTextTitle"
                        },

                        //SKIN radio
                        new SkiaWrap()
                        {
                            HorizontalOptions = LayoutOptions.Center,
                            Children =
                            {
                                new DrawnRadioButton(TextColor)
                                {
                                    Text = ResStrings.SkinDefault, VerticalOptions = LayoutOptions.Center
                                },
                                new DrawnRadioButton(TextColor)
                                {
                                    Text = ResStrings.SkinRed, VerticalOptions = LayoutOptions.Center
                                },
                            }
                        }.Assign(out RadiosSkin),

                        //STEPS
                        new SkiaLabel(ResStrings.Steps)
                        {
                            Margin = new(0, 16, 0, 0),
                            HorizontalOptions = LayoutOptions.Center,
                            FontSize = 18,
                            MaxLines = 1,
                            TextColor = TextColor,
                            FontFamily = "FontTextTitle"
                        },
                        new SkiaShape()
                        {
                            HorizontalOptions = LayoutOptions.Center,
                            StrokeColor = TextColor,
                            StrokeWidth = StrokeWidth,
                            VerticalOptions = LayoutOptions.Start,
                            UseCache = SkiaCacheType.Image,
                            CornerRadius = 12,
                            HeightRequest = 38,
                            WidthRequest = 150,
                            BackgroundColor = Colors.Transparent,
                            Children =
                            {
                                new SkiaMarkdownLabel(ResStrings.BtnCreate)
                                {
                                    HorizontalOptions = LayoutOptions.Center,
                                    VerticalOptions = LayoutOptions.Center,
                                    MaxLines = 1,
                                    FontSize = 14,
                                    TextColor = TextColor,
                                    FontFamily = "FontTextTitle"
                                }
                            }
                        }.OnTapped((me) =>
                        {
                            Debug.WriteLine("ADD");
                            EditItem(null);
                        }),

                        //EDITABLE LIST
                        new SkiaScroll()
                        {
                            VerticalOptions = LayoutOptions.Fill,
                            HorizontalOptions = LayoutOptions.Fill,
                            //Bounces = false,
                            Content = new SkiaStack()
                            {
                                UseCache = SkiaCacheType.Image,
                                ItemsSource = AvailableTimers,
                                ItemTemplate = new DataTemplate(() =>
                                {
                                    SkiaLabel cellTitle;
                                    SkiaLabel cellTime;

                                    var cell = new SkiaGrid()
                                        {
                                            UseCache = SkiaCacheType.Image,
                                            HeightRequest = 38,
                                            ColumnSpacing = 8,
                                            Children =
                                            {
                                                //time
                                                new SkiaMarkdownLabel()
                                                    {
                                                        TextColor = TextColor,
                                                        FontFamily = "FontTextTitle",
                                                        VerticalOptions = LayoutOptions.Center
                                                    }
                                                    .Assign(out cellTime).WithColumn(0),

                                                // title
                                                new SkiaMarkdownLabel()
                                                    {
                                                        TextColor = TextColor,
                                                        FontFamily = "FontTextTitle",
                                                        MaxLines = 1,
                                                        VerticalOptions = LayoutOptions.Fill,
                                                        HorizontalOptions = LayoutOptions.Fill,
                                                        VerticalTextAlignment = TextAlignment.Center
                                                    }
                                                    .OnTapped(me =>
                                                    {
                                                        var timerStep = me.BindingContext as TimerStep;
                                                        if (timerStep != null)
                                                        {
                                                            // Long press to edit, single tap to select as current
                                                            SelectCurrentTimer(timerStep);
                                                        }
                                                    })
                                                    .OnLongPressing(me => EditItem(me.BindingContext as TimerStep))
                                                    .Assign(out cellTitle).WithColumn(1),

                                                // Current indicator
                                                new FontIconLabelDrawn()
                                                    {
                                                        Text = FaPro.Play,
                                                        FontSize = 12,
                                                        TextColor = TextColor,
                                                        HorizontalOptions = LayoutOptions.Center,
                                                        VerticalOptions = LayoutOptions.Center,
                                                        Margin = new Thickness(8, 0, 8, 0)
                                                    }
                                                    .Assign(out var currentIndicator).WithColumn(2),

                                                // DELETE
                                                new FontIconLabelDrawn()
                                                    {
                                                        Text = FaPro.TrashCan,
                                                        FontSize = 14,
                                                        TextColor = TextColor.WithAlpha(0.85f),
                                                        HorizontalOptions = LayoutOptions.Center,
                                                        VerticalOptions = LayoutOptions.Center,
                                                        Margin = new Thickness(0, 0, 12, 0)
                                                    }
                                                    .OnTapped(me => DeleteItem(me.BindingContext as TimerStep))
                                                    .WithColumn(3),
                                            }
                                        }.WithColumnDefinitions("Auto,*,32,32")
                                        .ObserveSelf((me, prop) =>
                                        {
                                            if (me.BindingContext is TimerStep ctx)
                                            {
                                                if (prop.IsEither(nameof(BindingContext)))
                                                {
                                                    cellTime.Text = $"{ctx.Time:mm\\:ss}";
                                                    cellTitle.Text = ctx.Title;
                                                    currentIndicator.IsVisible = CurrentTimer?.Timer == ctx;
                                                }
                                            }
                                        })
                                        .Observe(this, (me, prop) =>
                                        {
                                            if (prop.IsEither(nameof(CurrentTimer)) && me.BindingContext is TimerStep ctx)
                                            {
                                                currentIndicator.IsVisible = CurrentTimer?.Timer == ctx;
                                            }
                                        });
                                    return cell;
                                })
                            },
                        },

                        //BUTTONS SAVE ETC
                        new SkiaGrid()
                        {
                            HorizontalOptions = LayoutOptions.Fill,
                            Margin = new(24, 0, 24, 16),
                            ColumnSpacing = 16,
                            Children =
                            {
                                new SkiaShape()
                                {
                                    HorizontalOptions = LayoutOptions.End,
                                    StrokeColor = TextColor,
                                    StrokeWidth = StrokeWidth,
                                    VerticalOptions = LayoutOptions.Start,
                                    UseCache = SkiaCacheType.Image,
                                    CornerRadius = 12,
                                    HeightRequest = 38,
                                    WidthRequest = 150,
                                    BackgroundColor = Colors.Transparent,
                                    Children =
                                    {
                                        new SkiaMarkdownLabel(ResStrings.BtnClose)
                                        {
                                            HorizontalOptions = LayoutOptions.Center,
                                            VerticalOptions = LayoutOptions.Center,
                                            MaxLines = 1,
                                            FontSize = 14,
                                            TextColor = TextColor,
                                            FontFamily = "FontTextTitle"
                                        }
                                    }
                                }.OnTapped((me) =>
                                {
                                    Debug.WriteLine("CANCEL");
                                    VisibleScreen = 0;
                                }),
                                new SkiaShape()
                                {
                                    StrokeColor = TextColor,
                                    StrokeWidth = StrokeWidth,
                                    VerticalOptions = LayoutOptions.Start,
                                    UseCache = SkiaCacheType.Image,
                                    CornerRadius = 12,
                                    HeightRequest = 38,
                                    WidthRequest = 150,
                                    BackgroundColor = Colors.Transparent,
                                    Children =
                                    {
                                        new SkiaMarkdownLabel(ResStrings.BtnSave)
                                        {
                                            HorizontalOptions = LayoutOptions.Center,
                                            VerticalOptions = LayoutOptions.Center,
                                            MaxLines = 1,
                                            FontSize = 14,
                                            TextColor = TextColor,
                                            FontFamily = "FontTextTitle"
                                        }
                                    }
                                }.OnTapped((me) => { Debug.WriteLine("SAVE"); }).WithColumn(1),
                            }
                        }.WithColumnDefinitions("50*,50*")
                    }
                }.Observe(this, (me, prop) =>
                {
                    if (prop.IsEither(nameof(BindingContext), nameof(VisibleScreen)))
                    {
                        me.IsVisible = VisibleScreen == 1;
                    }
                }),

                //EDIT SCREEN
                new SkiaLayer()
                {
                    Tag = "EDITOR-2",
                    VerticalOptions = LayoutOptions.Fill,
                    Type = LayoutType.Column,
                    Spacing = 24,
                    Padding = new(16, 24, 16, 24),
                    HorizontalOptions = LayoutOptions.Fill,
                    Children = new List<SkiaControl>()
                    {
                        new SkiaMarkdownLabel(ResStrings.BtnEdit)
                        {
                            HorizontalOptions = LayoutOptions.Center,
                            FontSize = 24,
                            MaxLines = 1,
                            TextColor = TextColor,
                            FontFamily = "FontTextTitle"
                        }.Assign(out LabelEditorTitle),
                        new SkiaMarkdownLabel(ResStrings.Title)
                        {
                            HorizontalOptions = LayoutOptions.Center,
                            FontSize = 18,
                            MaxLines = 1,
                            TextColor = TextColor,
                            FontFamily = "FontTextTitle"
                        },

                        // ENTRY for TITLE
                        new SkiaShape()
                        {
                            StrokeWidth = StrokeWidth,
                            StrokeColor = TextColor,
                            CornerRadius = 8,
                            HorizontalOptions = LayoutOptions.Fill,
                            HeightRequest = 44,
                            Padding = new(6, 0),
                            Children =
                            {
                                new SkiaMauiEntry()
                                {
                                    VerticalOptions = LayoutOptions.Fill,
                                    TextColor = TextColor,
                                    FontSize = 15,
                                    FontFamily = "FontText",
                                    Placeholder = "...",
                                    PlaceholderColor = TextColor.WithAlpha(0.75f),
                                    HorizontalOptions = LayoutOptions.Fill
                                }.Assign(out EntryEditor)
                            }
                        },

                        //TIME WHEELS
                        new SkiaLabel(ResStrings.Time)
                        {
                            Margin = new(0, 4, 0, 0),
                            HorizontalOptions = LayoutOptions.Center,
                            FontSize = 18,
                            MaxLines = 1,
                            TextColor = TextColor,
                            FontFamily = "FontTextTitle"
                        },
                        new SkiaRow()
                        {
                            HeightRequest = 200,
                            HorizontalOptions = LayoutOptions.Center,
                            Children =
                            {
                                // MINS
                                new SkiaWheelPicker()
                                    {
                                        BackgroundView =
                                            new SkiaLayer()
                                            {
                                                VerticalOptions = LayoutOptions.Fill, FillGradient = pickerGradient,
                                            },
                                        WidthRequest = 75,
                                        VisibleItems = 7,
                                        UseCache = SkiaCacheType.Operations,
                                        HeightRequest = -1,
                                        HorizontalOptions = LayoutOptions.Start,
                                        VerticalOptions = LayoutOptions.Fill,
                                        LinesColor = TextColor.MultiplyAlpha(0.33f),
                                        TextColor = TextColor,
                                        ItemsSource = MinutesSource,
                                    }.Assign(out PickerMinutes)
                                    //two-way bindings
                                    //way A
                                    .ObserveSelf((me, prop) =>
                                    {
                                        if (prop.IsEither(nameof(BindingContext),
                                                nameof(WheelPicker.SelectedIndex)))
                                        {
                                            SelectedMinutesIndex = me.SelectedIndex;
                                        }
                                    })
                                    //way B
                                    .Observe(this, (me, prop) =>
                                    {
                                        if (prop.IsEither(nameof(BindingContext), nameof(SelectedMinutesIndex)))
                                        {
                                            me.SelectedIndex = SelectedMinutesIndex;
                                        }
                                    }),
                                new SkiaMarkdownLabel(ResStrings.X_Mins)
                                {
                                    VerticalOptions = LayoutOptions.Center,
                                    FontSize = 14,
                                    MaxLines = 1,
                                    TextColor = TextColor,
                                    FontFamily = "FontTextTitle"
                                },

                                //SECS
                                new SkiaWheelPicker()
                                    {
                                        Margin = new(8, 0, 0, 0),
                                        BackgroundView =
                                            new SkiaLayer()
                                            {
                                                VerticalOptions = LayoutOptions.Fill, FillGradient = pickerGradient,
                                            },
                                        WidthRequest = 75,
                                        VisibleItems = 7,
                                        UseCache = SkiaCacheType.Operations,
                                        HeightRequest = -1,
                                        HorizontalOptions = LayoutOptions.Start,
                                        VerticalOptions = LayoutOptions.Fill,
                                        LinesColor = TextColor.MultiplyAlpha(0.33f),
                                        TextColor = TextColor,
                                        ItemsSource = SecondsSource,
                                    }.Assign(out PickerSeconds)
                                    //two-way bindings
                                    //way A
                                    .ObserveSelf((me, prop) =>
                                    {
                                        if (prop.IsEither(nameof(BindingContext),
                                                nameof(WheelPicker.SelectedIndex)))
                                        {
                                            SelectedSecondsIndex = me.SelectedIndex;
                                        }
                                    })
                                    //way B
                                    .Observe(this, (me, prop) =>
                                    {
                                        if (prop.IsEither(nameof(BindingContext), nameof(SelectedSecondsIndex)))
                                        {
                                            me.SelectedIndex = SelectedSecondsIndex;
                                        }
                                    }),
                                new SkiaMarkdownLabel(ResStrings.X_Secs)
                                {
                                    VerticalOptions = LayoutOptions.Center,
                                    FontSize = 14,
                                    MaxLines = 1,
                                    TextColor = TextColor,
                                    FontFamily = "FontTextTitle"
                                },
                            }
                        },

                        //BUTTONS SAVE ETC
                        new SkiaGrid()
                        {
                            HorizontalOptions = LayoutOptions.Fill,
                            Margin = new(24, 14, 24, 16),
                            ColumnSpacing = 16,
                            Children =
                            {
                                // CANCEL
                                new SkiaShape()
                                {
                                    HorizontalOptions = LayoutOptions.End,
                                    StrokeColor = TextColor,
                                    StrokeWidth = StrokeWidth,
                                    VerticalOptions = LayoutOptions.Start,
                                    UseCache = SkiaCacheType.Image,
                                    CornerRadius = 12,
                                    HeightRequest = 38,
                                    WidthRequest = 150,
                                    BackgroundColor = Colors.Transparent,
                                    Children =
                                    {
                                        new SkiaMarkdownLabel(ResStrings.BtnCancel)
                                        {
                                            HorizontalOptions = LayoutOptions.Center,
                                            VerticalOptions = LayoutOptions.Center,
                                            MaxLines = 1,
                                            FontSize = 14,
                                            TextColor = TextColor,
                                            FontFamily = "FontTextTitle"
                                        }
                                    }
                                }.OnTapped((me) =>
                                {
                                    Debug.WriteLine("CANCEL");
                                    GoBackFromEditor();
                                }),

                                //SAVE
                                new SkiaShape()
                                {
                                    StrokeColor = TextColor,
                                    StrokeWidth = StrokeWidth,
                                    VerticalOptions = LayoutOptions.Start,
                                    UseCache = SkiaCacheType.Image,
                                    CornerRadius = 12,
                                    HeightRequest = 38,
                                    WidthRequest = 150,
                                    BackgroundColor = Colors.Transparent,
                                    Children =
                                    {
                                        new SkiaMarkdownLabel(ResStrings.BtnSave)
                                        {
                                            HorizontalOptions = LayoutOptions.Center,
                                            VerticalOptions = LayoutOptions.Center,
                                            MaxLines = 1,
                                            FontSize = 14,
                                            TextColor = TextColor,
                                            FontFamily = "FontTextTitle"
                                        }
                                    }
                                }.OnTapped((me) =>
                                {
                                    Debug.WriteLine("SAVE");
                                    SaveEditor();
                                }).WithColumn(1),
                            }
                        }.WithColumnDefinitions("50*,50*")
                    }
                }.Observe(this, (me, prop) =>
                {
                    if (prop.IsEither(nameof(BindingContext), nameof(VisibleScreen)))
                    {
                        me.IsVisible = VisibleScreen == 2;
                    }
                })
            };

            RadioButtons.All.Select(RadiosSkin, Preferences.Get("TimerSkin", 0));
        }

        private SkiaLayout RadiosSkin;

        private void OnRadioChanged(object? sender, EventArgs e)
        {
            if (RadiosSkin != null)
            {
                var selected = RadioButtons.All.GetSelectedIndex(RadiosSkin);
                if (selected >=0)
                {
                    SelectSkin(selected);
                }
            }
        }

        #endregion

        #region EDITOR

        private SkiaMauiEntry? EntryEditor;
        private SkiaLabel LabelEditorTitle;

        /// <summary>
        /// Add or Edit item
        /// </summary>
        /// <param name="item"></param>
        private void EditItem(TimerStep? item)
        {
            EditingItem = item;

            if (item == null)
            {
                //create
                Item = new() { Title = "", Time = new TimeSpan(0, 1, 0) };
                LabelEditorTitle.Text = ResStrings.NewStep;
            }
            else
            {
                //edit
                Item = Reflection.Clone(item);
                LabelEditorTitle.Text = ResStrings.EditStep;
            }

            SetupEditor();

            VisibleScreen = 2;
        }

        private TimerStep? EditingItem;

        void SetupEditor()
        {
            EntryEditor.Text = Item.Title;

            var mins = Item.Time.Minutes.ToString();
            var secs = Item.Time.Seconds.ToString();
            var itemMins = Minutes.FirstOrDefault(x => x.Title == mins);
            var itemSecs = Minutes.FirstOrDefault(x => x.Title == secs);

            PickerMinutes.SelectedIndex = itemMins != null ? Minutes.IndexOf(itemMins) : 0;
            PickerSeconds.SelectedIndex = itemSecs != null ? Minutes.IndexOf(itemSecs) : 0;
        }

        void SaveEditor()
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(EntryEditor?.Text))
            {
                // Could show an error message here
                return;
            }

            // Get values from UI
            var title = EntryEditor.Text.Trim();
            var minutes = PickerMinutes.SelectedIndex >= 0 && PickerMinutes.SelectedIndex < MinutesSource.Count
                ? MinutesSource[PickerMinutes.SelectedIndex].Value
                : 0;
            var seconds = PickerSeconds.SelectedIndex >= 0 && PickerSeconds.SelectedIndex < SecondsSource.Count
                ? SecondsSource[PickerSeconds.SelectedIndex].Value
                : 0;

            var timeSpan = new TimeSpan(0, (int)minutes, (int)seconds);

            if (timeSpan.TotalSeconds <= 0)
            {
                // Timer must be at least 1 second
                return;
            }

            if (EditingItem == null) //new
            {
                // Create new timer step
                var newStep = new TimerStep
                {
                    Title = title,
                    Time = timeSpan
                };

                AvailableTimers.Add(newStep);
                Timers.Add(new RunningTimer { Timer = newStep });

                // Save to preferences
                TimerStep.Save(AvailableTimers);
            }
            else
            {
                // Update existing timer step
                EditingItem.Title = title;
                EditingItem.Time = timeSpan;

                // Find and update the corresponding running timer
                var runningTimer = Timers.FirstOrDefault(t => t.Timer == EditingItem);
                if (runningTimer != null)
                {
                    // Reset the running timer if it was the current one
                    if (runningTimer == CurrentTimer && IsRunning)
                    {
                        StopTimer();
                    }
                }

                // Save to preferences
                TimerStep.Save(AvailableTimers);
            }

            GoBackFromEditor();
        }

        void GoBackFromEditor()
        {
            VisibleScreen = 1;
        }

        /// <summary>
        /// MODEL FOR EDITOR
        /// </summary>
        protected TimerStep Item { get; set; }

        private void DeleteItem(TimerStep item)
        {
            if (item == null) return;

            // Find the corresponding running timer
            var runningTimer = Timers.FirstOrDefault(t => t.Timer == item);

            // If this is the current timer and it's running, stop it
            if (runningTimer == CurrentTimer && IsRunning)
            {
                StopTimer();
            }

            // Remove from collections
            AvailableTimers.Remove(item);
            if (runningTimer != null)
            {
                Timers.Remove(runningTimer);
            }

            // If we deleted the current timer, select a new one
            if (runningTimer == CurrentTimer && Timers.Count > 0)
            {
                CurrentTimer = Timers[0];
            }

            // Save to preferences
            TimerStep.Save(AvailableTimers);
        }

        private void SelectCurrentTimer(TimerStep timerStep)
        {
            var runningTimer = Timers.FirstOrDefault(t => t.Timer == timerStep);
            if (runningTimer != null)
            {
                // Stop current timer if running
                if (IsRunning)
                {
                    StopTimer();
                }

                CurrentTimer = runningTimer;
                CurrentTimer.Time = CurrentTimer.Timer.Time;
                CurrentTimer.Progress = 0;
                OnPropertyChanged(nameof(CurrentTimer));
            }
        }

        #endregion

        #region PICKER TIME

        void InitPickers()
        {
            Minutes = new List<ValueItem>();
            Minutes.AddRange(Enumerable.Range(0, 60).Select(x => new ValueItem { Title = x.ToString(), Value = x })
                .ToList());

            SecondsSource = Minutes.ToList();
            MinutesSource = Minutes.ToList();
        }

        /// <summary>
        /// Data source for wheel picker
        /// </summary>
        public List<ValueItem> MinutesSource { get; set; }

        /// <summary>
        /// Data source for wheel picker
        /// </summary>
        public List<ValueItem> SecondsSource { get; set; }

        private SkiaWheelPicker PickerMinutes;
        private SkiaWheelPicker PickerSeconds;

        private ValueItem _selectedSeconds;

        public ValueItem SelectedSeconds
        {
            get { return _selectedSeconds; }
            set
            {
                if (_selectedSeconds != value)
                {
                    _selectedSeconds = value;
                    OnPropertyChanged();
                }
            }
        }

        public List<ValueItem> Minutes { get; set; }


        private int _SelectedMinutesIndex = -1;

        public int SelectedMinutesIndex
        {
            get { return _SelectedMinutesIndex; }
            set
            {
                if (_SelectedMinutesIndex != value)
                {
                    _SelectedMinutesIndex = value;
                    OnPropertyChanged();
                }
            }
        }

        private int _SelectedSecondsIndex = -1;

        public int SelectedSecondsIndex
        {
            get { return _SelectedSecondsIndex; }
            set
            {
                if (_SelectedSecondsIndex != value)
                {
                    _SelectedSecondsIndex = value;
                    OnPropertyChanged();
                }
            }
        }

        #endregion
    }
}
