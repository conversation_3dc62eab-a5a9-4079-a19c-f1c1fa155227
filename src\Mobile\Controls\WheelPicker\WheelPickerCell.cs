﻿using AppoMobi.Models;
using DrawnUi.Controls;
using TextColors = AppoMobi.Xam.TextColors;

namespace AppoMobi.Mobile.Views.Controls.WheelPicker
{

    public interface IWheelPickerCell
    {
        void UpdateContext(WheelCellInfo ctx);
    }

    public class WheelPickerCell : SkiaLayout, IWheelPickerCell
    {
        private readonly SkiaLabel _label;

        public WheelPickerCell()
        {
            HorizontalOptions = LayoutOptions.Fill; ;
            Padding = 0;
            UseCache = SkiaCacheType.Operations;

            Children = new List<SkiaControl>()
            {
                new SkiaLayer() //will be "filled" by picker
                {
                    Children =
                    {
                        new SkiaLabel()
                        {
                            HeightRequest = 100,
                            //Padding = new Thickness(0,4,5,4), //BUG: NOT working
                            FontAttributes = FontAttributes.Italic,
                            FontSize = 17,
                            //BackgroundColor = Colors.DarkRed,
                            //MonoForDigits="1",
                            HorizontalOptions = LayoutOptions.Center,
                            TextColor = TextColors.Result,
                            VerticalOptions = LayoutOptions.Center,
                            VerticalTextAlignment = TextAlignment.Center,
                            HorizontalTextAlignment = DrawTextAlignment.Center
                        }.Assign(out _label)
                    }
                }
            };

            ApplyContext();
        }

        void ApplyContext()
        {
            if (BindingContext is ValueItem value)
            {
                _label.Text = value.Title;
            }
        }


        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            ApplyContext();
        }

        public void UpdateContext(WheelCellInfo ctx)
        {
            Opacity = ctx.Opacity;
            //if (ctx.IsSelected)
            //{
            //    _label.FontAttributes = FontAttributes.Italic | FontAttributes.Bold;
            //}
            //else
            //{
            //    _label.FontAttributes = FontAttributes.Italic;
            //}
        }
    }
}
