﻿<?xml version="1.0" encoding="utf-8"?>

<draw:SkiaLayout
    x:Class="AppoMobi.Maui.DrawnUi.Demo.Views.Controls.WheelPicker"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:draw1="clr-namespace:DrawnUi.Draw"
    xmlns:specials="clr-namespace:AppoMobi.Specials;assembly=AppoMobi.Specials"
    xmlns:wheelPicker="clr-namespace:AppoMobi.Mobile.Views.Controls.WheelPicker"
    xmlns:xam="using:AppoMobi.Xam"
    x:Name="Container"
    BackgroundColor="Transparent"
    HeightRequest="210"
    HorizontalOptions="Center"
    WidthRequest="130">

    <draw:SkiaLayout
        HorizontalOptions="Fill"
        Opacity="0.33"
        UseCache="Image"
        VerticalOptions="Fill">
        <draw:SkiaControl.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                <GradientStop Offset="0.0" Color="{x:Static xam:BackColors.GradientEndNav}" />
                <GradientStop Offset="0.5" Color="{x:Static xam:BackColors.GradientStartNav}" />
                <GradientStop Offset="1.0" Color="{x:Static xam:BackColors.GradientEndNav}" />
            </LinearGradientBrush>
        </draw:SkiaControl.Background>
    </draw:SkiaLayout>

    <!--  MODERN  -->
    <draw:SkiaWheelScroll
        x:Name="Scroller"
        Margin="0,10,0,10"
        Bounces="True"
        FrictionScrolled="0.8"
        HorizontalOptions="Fill"
        LinesColor="{Binding Source={x:Reference Container}, Path=LinesColor}"
        Loop="False"
        OpacityFadeStrength="1.5"
        Orientation="Vertical"
        SnapToChildren="Center"
        Tag="WheelScroll"
        TrackIndexPosition="Center"
        VerticalOptions="Fill"
        ZIndex="1">

        <draw:SkiaWheelStack
            x:Name="Wheel"
            HorizontalOptions="Fill"
            ItemTemplateType="{x:Type wheelPicker:WheelPickerCell}"
            Spacing="1"
            Type="Column"
            VerticalOptions="Fill"
            WidthRequest="-1">

        </draw:SkiaWheelStack>

    </draw:SkiaWheelScroll>


</draw:SkiaLayout>